import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { ServiceHero } from "@/components/service-hero"
import { ServiceBenefits } from "@/components/service-benefits"
import { ServiceHowItWorks } from "@/components/service-how-it-works"
import { ServiceFAQ } from "@/components/service-faq"
import { ServiceCTA } from "@/components/service-cta"

export const metadata = {
  title: "Application Modernization Services | Legacy System Upgrade | Nexveria",
  description:
    "Upgrade outdated software to modern technology. Faster performance, lower costs, and new features without starting from scratch.",
}

export default function ApplicationModernizationPage() {
  const benefits = [
    {
      title: "Stop Throwing Money at Old Tech",
      description:
        "Legacy systems cost 3-5x more to maintain than modern applications. High hosting fees, expensive developers, constant patches. Modernization cuts these costs by 60-70%.",
      metric: "60-70% Cost Cut",
    },
    {
      title: "Move Faster, Build More",
      description:
        "Old systems take weeks to add simple features. Modern architecture enables new features in days. Your business can respond to market changes before competitors even notice.",
      metric: "10x Faster",
    },
    {
      title: "Security That Actually Works",
      description:
        "Legacy software has vulnerabilities that can't be patched. Modern systems have built-in security, automatic updates, and compliance features. Reduce breach risk by 90%.",
      metric: "90% Safer",
    },
  ]

  const steps = [
    {
      number: "1",
      title: "System Assessment & Planning",
      description:
        "We audit your current application: what works, what's broken, what's costing too much. Identify the business-critical features that must be preserved. Create a phased modernization roadmap that keeps the business running while we rebuild.",
    },
    {
      number: "2",
      title: "Phased Migration",
      description:
        "Modernize in stages, not all at once. Start with the most painful or costly module. Migrate data carefully, rewrite code using modern frameworks, and run old and new systems in parallel until you're confident. No big bang, no business disruption.",
    },
    {
      number: "3",
      title: "Launch & Enhancement",
      description:
        "Deploy the modernized system, train your team, and monitor performance. With modern architecture in place, we add the features you've wanted for years but couldn't build. Your application becomes a competitive advantage, not a liability.",
    },
  ]

  const approachOptions = [
    {
      title: "Replatforming (Lift & Reshape)",
      description: "Move to cloud or modern infrastructure with minimal code changes",
      bestFor: "Applications that work well but run on outdated infrastructure",
      timeline: "2-4 months",
    },
    {
      title: "Refactoring (Rearchitecture)",
      description: "Rebuild core components using modern frameworks and patterns",
      bestFor: "Applications with major performance or scalability issues",
      timeline: "4-8 months",
    },
    {
      title: "Complete Rebuild",
      description: "Start fresh with modern technology while preserving business logic",
      bestFor: "When legacy code is unmaintainable or technology is obsolete",
      timeline: "6-12 months",
    },
  ]

  const faqs = [
    {
      question: "Can we modernize without disrupting our business operations?",
      answer:
        "Yes. We use phased migration strategies. The old system keeps running while we build the new one. We migrate users gradually, starting with internal teams, then small user groups, then everyone. At any point, we can roll back. Most clients report zero customer-facing disruption.",
    },
    {
      question: "How do we know modernization is worth the investment?",
      answer:
        "We calculate ROI upfront. Typical benefits: 60% lower maintenance costs, 10x faster feature development, 90% reduction in downtime, and ability to scale. If the numbers don't work, we'll tell you to stick with what you have and just add specific improvements.",
    },
    {
      question: "What if we lose important features or data during migration?",
      answer:
        "Feature preservation is priority one. We document everything your system does, ensure the modern version replicates it, and run parallel testing. Data migration includes validation at every step. We don't cut over until we've proven 100% feature parity and data integrity.",
    },
    {
      question: "Our original developers are long gone. Can you still modernize?",
      answer:
        "Absolutely. We specialize in undocumented legacy systems. We reverse-engineer the application, understand the business logic through testing and user interviews, then rebuild it properly. Often we discover features even you didn't know existed.",
    },
    {
      question: "Should we modernize or build new from scratch?",
      answer:
        "Depends on the state of your code and how much business logic it contains. If the system works but is outdated, modernize (faster and cheaper). If it's a mess of spaghetti code with minimal unique logic, rebuild. We'll assess and recommend the approach that makes business sense.",
    },
    {
      question: "What technology stack will you use?",
      answer:
        "We don't have a one-size-fits-all answer. Modern stacks like React/Next.js for frontend, Node.js/Python for backend, PostgreSQL/MongoDB for databases, and cloud hosting are common. But we choose based on your team's skills, budget, and long-term needs, not our preferences.",
    },
    {
      question: "How long does modernization typically take?",
      answer:
        "Simple replatforming: 2-4 months. Refactoring major components: 4-8 months. Complete rebuild: 6-12 months. We deliver working increments every 2 weeks, so you see progress continuously. The exact timeline depends on application complexity and how much we modernize at once.",
    },
    {
      question: "What happens to our current development team?",
      answer:
        "We work with them, not replace them. They know the business logic better than anyone. We pair with them during modernization, transfer knowledge about the new stack, and help them become experts in modern development. After our engagement, they maintain and enhance the modernized system.",
    },
  ]

  return (
    <main className="min-h-screen">
      <Navbar />
      <ServiceHero
        subtitle="Application Modernization"
        title="Your Software Is Bleeding Money and Holding You Back"
        description="Legacy systems cost 3x more to maintain, can't scale, and prevent innovation. Modernize to cut costs, move faster, and finally build the features your business needs."
        painPoint="Every change takes weeks. Every developer is expensive and hard to find. Every day, you're losing ground to competitors with modern tech stacks."
        showContactForm={true}
        formTitle="Get Modernization Assessment"
      />

      <ServiceBenefits title="Why Modernize Now?" benefits={benefits} />

      <ServiceHowItWorks steps={steps} />

      <section className="container mx-auto px-6 py-20 bg-muted/30">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">Our Modernization Approaches</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {approachOptions.map((option, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
              >
                <h3 className="text-xl font-semibold mb-3">{option.title}</h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">{option.description}</p>
                <div className="mb-2">
                  <span className="text-sm font-semibold text-foreground">Best for:</span>
                  <p className="text-sm text-muted-foreground">{option.bestFor}</p>
                </div>
                <div>
                  <span className="text-sm font-semibold text-foreground">Timeline:</span>
                  <p className="text-sm text-accent">{option.timeline}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">Technologies We Modernize From</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              "Legacy .NET Framework",
              "Old PHP Applications",
              "Java EE / J2EE",
              "Classic ASP",
              "Legacy Angular/React",
              "Outdated Databases",
              "Monolithic Architecture",
              "On-Premise Only",
            ].map((tech, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-4 text-sm font-medium text-center hover:border-accent/50 transition-colors"
              >
                {tech}
              </div>
            ))}
          </div>
          <p className="text-center text-muted-foreground mt-8">
            If it's outdated and causing problems, we can modernize it
          </p>
        </div>
      </section>

      <ServiceFAQ faqs={faqs} />

      <ServiceCTA
        title="Ready to Modernize Your Legacy System?"
        description="Book a free technical assessment. We'll audit your application, identify modernization opportunities, and show you the ROI. No sales pressure, just honest engineering advice."
        primaryCTA="Get Free Assessment"
        secondaryCTA="See Other Services"
        secondaryHref="/services"
      />

      <Footer />
    </main>
  )
}
