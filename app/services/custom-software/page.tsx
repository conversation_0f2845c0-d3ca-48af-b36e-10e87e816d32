import { Navbar } from "@/components/navbar"
import { ServiceHero } from "@/components/service-hero"
import { LogoCarousel } from "@/components/logo-carousel"
import { ServicesGrid } from "@/components/services-grid"
import { StatsSection } from "@/components/stats-section"
import { ProjectsShowcase } from "@/components/projects-showcase"
import { DevelopmentServices } from "@/components/development-services"
import { AIToolsSection } from "@/components/ai-tools-section"
import { CustomersSection } from "@/components/customers-section"
import { IndustryExpertise } from "@/components/industry-expertise"
import { EngagementModels } from "@/components/engagement-models"
import { QualityApproach } from "@/components/quality-approach"
import { ProblemCards } from "@/components/problem-cards"
import { ComparisonSection } from "@/components/comparison-section"
import { WhyWorkWithUs } from "@/components/why-work-with-us"
import { ContactSection } from "@/components/contact-section"
import { Footer } from "@/components/footer"

export const metadata = {
  title: "Custom Software Development | Nexveria",
  description:
    "Build scalable, high-quality software tailored to your business needs. From web apps to AI solutions, we turn your complex requirements into working systems.",
}

export default function CustomSoftwarePage() {
  return (
    <main className="min-h-screen">
      <Navbar />
      <ServiceHero
        subtitle="Custom Software Development"
        title="Custom Software Development Company Focused on Your Success"
        description="Partner with technical experts who understand your business goals and deliver scalable, high-quality software solutions that drive real results."
        showContactForm={true}
        formTitle="Start Your Project"
      />
      <LogoCarousel />
      <ServicesGrid />
      <StatsSection />
      <ProjectsShowcase />
      <DevelopmentServices />
      <AIToolsSection />
      <CustomersSection />
      <IndustryExpertise />
      <EngagementModels />
      <QualityApproach />
      <ProblemCards />
      <ComparisonSection />
      <WhyWorkWithUs />
      <ContactSection />
      <Footer />
    </main>
  )
}
