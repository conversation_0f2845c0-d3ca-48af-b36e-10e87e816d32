import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { ServiceHero } from "@/components/service-hero"
import { ServiceBenefits } from "@/components/service-benefits"
import { ServiceHowItWorks } from "@/components/service-how-it-works"
import { ServiceFAQ } from "@/components/service-faq"
import { ServiceCTA } from "@/components/service-cta"

export const metadata = {
  title: "Business Intelligence Services | Data Dashboards & Analytics | Nexveria",
  description:
    "Turn data into actionable insights. Custom dashboards, automated reporting, and analytics that help you make better decisions faster.",
}

export default function BusinessIntelligencePage() {
  const benefits = [
    {
      title: "Make Decisions with Data, Not Guesses",
      description:
        "Stop relying on gut feel and spreadsheets. Live dashboards show exactly what's happening in your business. Revenue, customers, operations – all visible in real-time.",
      metric: "Real-Time Data",
    },
    {
      title: "Spot Problems Before They Cost You",
      description:
        "Automated alerts when metrics drop. Dashboards highlight trends before they become crises. Catch declining sales, inventory issues, or customer churn early enough to fix them.",
      metric: "Early Warning",
    },
    {
      title: "Stop Wasting Time on Reports",
      description:
        "Your team spends 10 hours weekly pulling reports manually. BI automates this completely. Reports generate themselves, dashboards update automatically, insights are always current.",
      metric: "10hrs/week Saved",
    },
  ]

  const steps = [
    {
      number: "1",
      title: "Metrics & Dashboard Planning",
      description:
        "Identify the metrics that actually matter for your business. Not vanity metrics, but KPIs that drive decisions. Design dashboards for different roles: executives see high-level trends, managers see operational details.",
    },
    {
      number: "2",
      title: "Data Connection & Visualization",
      description:
        "Connect your data sources to BI tools (Tableau, Power BI, or Looker). Build interactive dashboards with drill-downs, filters, and dynamic updates. You see not just numbers, but the story behind them.",
    },
    {
      number: "3",
      title: "Automation & Distribution",
      description:
        "Automate report generation and distribution. Executives get daily summaries. Teams get weekly performance reports. Alerts trigger when thresholds are crossed. Everyone has the data they need, when they need it.",
    },
  ]

  const solutions = [
    {
      title: "Executive Dashboards",
      items: [
        "Revenue and profitability tracking",
        "Key performance indicators (KPIs)",
        "Trend analysis and forecasting",
        "Goal tracking and progress",
        "Executive summary reports",
      ],
    },
    {
      title: "Sales Analytics",
      items: [
        "Sales pipeline visibility",
        "Conversion rate analysis",
        "Sales team performance",
        "Customer acquisition costs",
        "Revenue by product/region",
      ],
    },
    {
      title: "Operational Dashboards",
      items: [
        "Inventory and supply chain",
        "Production and efficiency metrics",
        "Resource utilization",
        "Quality and defect tracking",
        "Process performance monitoring",
      ],
    },
    {
      title: "Customer Analytics",
      items: [
        "Customer lifetime value",
        "Churn prediction and analysis",
        "Segmentation and targeting",
        "Product usage patterns",
        "Support ticket analytics",
      ],
    },
  ]

  const faqs = [
    {
      question: "Which BI tool should we use: Tableau, Power BI, or Looker?",
      answer:
        "Depends on your ecosystem and budget. Power BI if you use Microsoft 365 (tighter integration, lower cost). Tableau for complex visualizations and large datasets. Looker for technical teams comfortable with SQL. We recommend based on your needs, not vendor partnerships.",
    },
    {
      question: "Can non-technical people use BI dashboards?",
      answer:
        "That's the whole point. We design dashboards for business users, not data analysts. Click to filter, hover for details, drill down for specifics. No SQL, no technical knowledge needed. If you can use a website, you can use our dashboards.",
    },
    {
      question: "How long does it take to build dashboards?",
      answer:
        "Simple dashboard: 1-2 weeks. Comprehensive BI system with multiple dashboards: 4-8 weeks. We start with highest-value dashboards first. You get working dashboards within weeks, then we expand coverage.",
    },
    {
      question: "What if our data is in multiple systems?",
      answer:
        "That's normal. BI tools connect to multiple sources: databases, cloud apps, spreadsheets, APIs. We either connect directly or build a data warehouse first (recommended for complex setups). Either way, you get unified dashboards.",
    },
    {
      question: "Can we get alerts when metrics drop?",
      answer:
        "Yes. We set up automated alerts based on your thresholds. Revenue drops 10%? Alert. Inventory below minimum? Alert. Customer complaints spike? Alert. Choose how you're notified: email, Slack, SMS, or dashboard notifications.",
    },
    {
      question: "How much do BI tools cost?",
      answer:
        "Power BI: KES 5,000-15,000/user/month. Tableau: KES 30,000-70,000/user/month. Looker: KES 50,000-100,000/user/month. Plus our development and setup costs. Seems expensive until you calculate the cost of bad decisions from lack of data.",
    },
    {
      question: "Can we update dashboards ourselves?",
      answer:
        "Yes. We train your team to modify dashboards, add metrics, and create new views. For major changes, we're available. But day-to-day updates and tweaks? You handle those independently.",
    },
  ]

  return (
    <main className="min-h-screen">
      <Navbar />
      <ServiceHero
        subtitle="Business Intelligence"
        title="Stop Making Decisions Based on Last Month's Spreadsheet"
        description="Build live dashboards that show what's actually happening in your business. Real-time metrics, automated reports, and insights that drive better decisions."
        painPoint="By the time you compile reports and analyze data, the information is outdated. You're flying blind, making decisions based on gut feel and stale numbers."
        showContactForm={true}
        formTitle="Get Free BI Consultation"
      />

      <ServiceBenefits title="What BI Actually Delivers" benefits={benefits} />

      <ServiceHowItWorks steps={steps} />

      <section className="container mx-auto px-6 py-20 bg-muted/30">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">BI Solutions We Build</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {solutions.map((solution, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
              >
                <h3 className="text-xl font-semibold mb-4">{solution.title}</h3>
                <ul className="space-y-2">
                  {solution.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-2 text-sm text-muted-foreground">
                      <span className="text-accent mt-1">•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="mb-6 text-3xl font-bold lg:text-4xl">BI Platforms We Master</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {[
              "Tableau",
              "Power BI",
              "Looker",
              "Metabase",
              "Google Data Studio",
              "Custom Dashboards",
            ].map((platform, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-4 text-sm font-medium hover:border-accent/50 transition-colors"
              >
                {platform}
              </div>
            ))}
          </div>
        </div>
      </section>

      <ServiceFAQ faqs={faqs} />

      <ServiceCTA
        title="Ready to See Your Business Clearly?"
        description="Book a free BI consultation. We'll review your reporting needs, identify key metrics, and show you what live dashboards could reveal about your business."
        primaryCTA="Get Free Consultation"
        secondaryCTA="See Other Services"
        secondaryHref="/services"
      />

      <Footer />
    </main>
  )
}
