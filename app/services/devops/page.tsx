import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { ServiceHero } from "@/components/service-hero"
import { ServiceBenefits } from "@/components/service-benefits"
import { ServiceHowItWorks } from "@/components/service-how-it-works"
import { ServiceFAQ } from "@/components/service-faq"
import { ServiceCTA } from "@/components/service-cta"

export const metadata = {
  title: "DevOps & Automation Services | CI/CD Pipeline Setup | Nexveria",
  description:
    "Deploy faster, break less, scale automatically. Modern DevOps practices that turn weeks of deployment into minutes of automated releases.",
}

export default function DevOpsPage() {
  const benefits = [
    {
      title: "Deploy 10x Faster",
      description:
        "Manual deployments take hours and require your best developers. Automated pipelines deploy in minutes, tested and validated. Push to production multiple times per day instead of once per month.",
      metric: "Minutes not Hours",
    },
    {
      title: "Break Things Less Often",
      description:
        "Automated testing catches bugs before production. Deployment automation eliminates human error. Rollback is instant if something goes wrong. Downtime drops by 90%.",
      metric: "90% Less Downtime",
    },
    {
      title: "Free Your Developers",
      description:
        "Stop wasting engineers on deployment babysitting. Infrastructure as code means juniors can deploy safely. Your senior developers focus on features, not server maintenance.",
      metric: "40hrs/week Saved",
    },
  ]

  const steps = [
    {
      number: "1",
      title: "Current State Analysis",
      description:
        "We map your deployment process, identify bottlenecks, and document pain points. How long does deployment take? How often does it fail? What manual steps exist? This baseline shows where we'll create the biggest impact.",
    },
    {
      number: "2",
      title: "Pipeline Implementation",
      description:
        "Build automated CI/CD pipelines: code commits trigger tests, passing tests trigger builds, builds deploy to staging then production. Add monitoring, alerts, and automatic rollbacks. Your deployment becomes a button press.",
    },
    {
      number: "3",
      title: "Infrastructure Automation",
      description:
        "Convert manual infrastructure changes to code. Servers, databases, networks defined in version control. Changes are reviewed, tested, and deployed automatically. Your infrastructure becomes reproducible and disaster-proof.",
    },
  ]

  const serviceAreas = [
    {
      title: "CI/CD Pipelines",
      items: [
        "Automated testing and builds",
        "Multi-environment deployments",
        "Blue-green & canary releases",
        "Automated rollback on failures",
        "Integration with GitHub/GitLab/Bitbucket",
      ],
    },
    {
      title: "Infrastructure as Code",
      items: [
        "Terraform for cloud resources",
        "Ansible for configuration",
        "Docker containerization",
        "Kubernetes orchestration",
        "Version-controlled infrastructure",
      ],
    },
    {
      title: "Monitoring & Observability",
      items: [
        "Application performance monitoring",
        "Log aggregation and analysis",
        "Custom alerts and dashboards",
        "Error tracking and debugging",
        "Uptime monitoring",
      ],
    },
    {
      title: "Security & Compliance",
      items: [
        "Secrets management",
        "Vulnerability scanning",
        "Automated security testing",
        "Compliance reporting",
        "Access control automation",
      ],
    },
  ]

  const faqs = [
    {
      question: "Our team has never used DevOps. Can we still implement it?",
      answer:
        "Absolutely. DevOps isn't a tool, it's a practice. We implement the automation and train your team. They learn by doing, with us guiding them. After 3 months, they're comfortable. After 6 months, they're proficient. We provide documentation and support throughout.",
    },
    {
      question: "How long until we see results?",
      answer:
        "First wins come fast. Simple CI/CD pipeline: 1-2 weeks. Automated testing: 2-4 weeks. Full infrastructure as code: 2-3 months. You'll see faster deployments within the first month. The full transformation takes 3-6 months, but benefits compound as we add each piece.",
    },
    {
      question: "Will DevOps work with our existing tech stack?",
      answer:
        "Yes. DevOps principles apply to any technology. We've automated deployments for .NET, Java, Python, Node.js, PHP, and more. On-premise servers, cloud, or hybrid. The tools change, but the approach works universally.",
    },
    {
      question: "What if a deployment fails automatically?",
      answer:
        "Automated doesn't mean uncontrolled. Failed tests stop deployment. Failed health checks trigger rollback. Every step has safety checks. You can require manual approval for production deployments if needed. Automation makes failure safer, not riskier.",
    },
    {
      question: "How much does DevOps tooling cost?",
      answer:
        "Many tools are free for small teams (GitHub Actions, GitLab CI, Jenkins). Paid tools typically cost KES 50,000-200,000/month for monitoring and security. The ROI is clear: if automation saves 40 developer hours per week, it pays for itself in reduced salary costs and faster time-to-market.",
    },
    {
      question: "Can we do DevOps without moving to the cloud?",
      answer:
        "Yes. DevOps works on-premise, though cloud makes it easier. We can automate deployments to your own servers, implement infrastructure as code for on-premise resources, and set up monitoring wherever your applications run.",
    },
  ]

  return (
    <main className="min-h-screen">
      <Navbar />
      <ServiceHero
        subtitle="DevOps & Automation"
        title="Stop Spending Fridays Deploying and Praying"
        description="Automate deployments, eliminate manual errors, and deploy confidently multiple times per day. Modern DevOps practices that turn deployment from a nightmare into a non-event."
        painPoint="Deployments take half a day, require your best developers, and still break production. Every release is stressful. Your team dreads Friday deployments."
        showContactForm={true}
        formTitle="Get DevOps Assessment"
      />

      <ServiceBenefits title="What DevOps Actually Does" benefits={benefits} />

      <ServiceHowItWorks steps={steps} />

      <section className="container mx-auto px-6 py-20 bg-muted/30">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">DevOps Services We Provide</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {serviceAreas.map((area, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
              >
                <h3 className="text-xl font-semibold mb-4">{area.title}</h3>
                <ul className="space-y-2">
                  {area.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-2 text-sm text-muted-foreground">
                      <span className="text-accent mt-1">•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="mb-6 text-3xl font-bold lg:text-4xl">Tools We Work With</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              "GitHub Actions",
              "GitLab CI/CD",
              "Jenkins",
              "Docker",
              "Kubernetes",
              "Terraform",
              "Ansible",
              "Prometheus",
              "Grafana",
              "ELK Stack",
              "AWS DevOps",
              "Azure DevOps",
            ].map((tool, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-4 text-sm font-medium hover:border-accent/50 transition-colors"
              >
                {tool}
              </div>
            ))}
          </div>
        </div>
      </section>

      <ServiceFAQ faqs={faqs} />

      <ServiceCTA
        title="Ready to Automate Your Deployments?"
        description="Book a free DevOps assessment. We'll analyze your deployment process, identify automation opportunities, and show you how much time and stress you'll save."
        primaryCTA="Get Free Assessment"
        secondaryCTA="See Other Services"
        secondaryHref="/services"
      />

      <Footer />
    </main>
  )
}
