import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { ServiceHero } from "@/components/service-hero"
import { ServiceBenefits } from "@/components/service-benefits"
import { ServiceHowItWorks } from "@/components/service-how-it-works"
import { ServiceFAQ } from "@/components/service-faq"
import { ServiceCTA } from "@/components/service-cta"

export const metadata = {
  title: "AI Chatbot Development | Intelligent Customer Service Bots | Nexveria",
  description:
    "Build AI-powered chatbots that actually understand customers. Handle support, sales, and lead qualification 24/7 without human intervention.",
}

export default function AIChatbotPage() {
  const benefits = [
    {
      title: "Handle 80% of Support Automatically",
      description:
        "AI chatbots answer common questions instantly. Password resets, order status, FAQs – resolved in seconds. Your support team handles only complex issues that actually need humans.",
      metric: "80% Automated",
    },
    {
      title: "Never Miss a Lead Again",
      description:
        "Chatbots qualify leads 24/7. Capture contact info, understand needs, and route to the right salesperson. Every website visitor gets immediate attention, even at 2 AM.",
      metric: "24/7 Coverage",
    },
    {
      title: "Scale Support Without Hiring",
      description:
        "One chatbot handles thousands of conversations simultaneously. As your business grows, support costs stay flat. No hiring, training, or managing customer service teams.",
      metric: "Unlimited Scale",
    },
  ]

  const steps = [
    {
      number: "1",
      title: "Conversation Design & Training",
      description:
        "We analyze your most common customer questions and complaints. Design conversation flows that sound natural, not robotic. Train the AI on your specific business, products, and policies. You approve all responses before launch.",
    },
    {
      number: "2",
      title: "Integration & Testing",
      description:
        "Connect the chatbot to your website, CRM, and business systems. It can check orders, access account info, and trigger actions. We test with real conversations, refining responses until it handles your scenarios perfectly.",
    },
    {
      number: "3",
      title: "Launch & Continuous Learning",
      description:
        "Deploy the chatbot to handle live conversations. Monitor performance, identify gaps, and continuously improve responses. The AI gets smarter over time, learning from every interaction.",
    },
  ]

  const capabilities = [
    {
      title: "Customer Support Automation",
      items: [
        "Answer FAQs instantly",
        "Troubleshoot common issues",
        "Process returns and refunds",
        "Check order status",
        "Escalate to humans when needed",
      ],
    },
    {
      title: "Sales & Lead Qualification",
      items: [
        "Qualify leads automatically",
        "Capture contact information",
        "Schedule appointments",
        "Product recommendations",
        "Route to sales team",
      ],
    },
    {
      title: "Multi-Channel Deployment",
      items: [
        "Website chat widget",
        "WhatsApp Business",
        "Facebook Messenger",
        "Telegram",
        "Custom mobile apps",
      ],
    },
    {
      title: "Advanced Features",
      items: [
        "Natural language understanding",
        "Multi-language support",
        "Sentiment analysis",
        "CRM integration",
        "Analytics and reporting",
      ],
    },
  ]

  const faqs = [
    {
      question: "Will the chatbot sound robotic and frustrate customers?",
      answer:
        "Not if built properly. We use modern AI (GPT-based models) that understand context and respond naturally. The bot admits when it doesn't understand and transfers to humans gracefully. Many customers don't realize they're talking to AI.",
    },
    {
      question: "What happens when the chatbot can't answer a question?",
      answer:
        "It transfers to a human agent seamlessly, providing conversation context so the human doesn't start from scratch. You define confidence thresholds: low-confidence answers get human review before sending. The bot learns from these escalations.",
    },
    {
      question: "Can the chatbot access our customer data and systems?",
      answer:
        "Yes, with proper security. We integrate with your CRM, order management, and databases. The chatbot can look up account info, check orders, and update records. All access is logged and follows your security policies.",
    },
    {
      question: "How accurate is AI chatbot understanding?",
      answer:
        "For well-defined domains, 85-95% accuracy. We train on your specific use cases, so it understands your industry jargon and product names. During development, we test hundreds of variations to ensure robust understanding.",
    },
    {
      question: "What if our products or policies change?",
      answer:
        "Easy to update. We provide a management dashboard where you can add new responses, update existing ones, and retrain the AI. Major changes take minutes, not weeks. No developer needed for basic updates.",
    },
    {
      question: "Can we use the chatbot for sales, not just support?",
      answer:
        "Absolutely. Sales chatbots qualify leads, answer product questions, handle objections, and schedule calls with your team. Many clients use chatbots to pre-qualify leads before salespeople engage, dramatically improving conversion rates.",
    },
    {
      question: "How long does chatbot development take?",
      answer:
        "Simple FAQ bot: 2-4 weeks. Advanced support bot with integrations: 6-8 weeks. Complex sales bot with multiple workflows: 8-12 weeks. You can start with basic functionality and add advanced features over time.",
    },
  ]

  return (
    <main className="min-h-screen">
      <Navbar />
      <ServiceHero
        subtitle="AI Chatbot Development"
        title="Stop Losing Customers Because You Can't Reply Fast Enough"
        description="AI chatbots that understand context, answer questions naturally, and escalate to humans when needed. Handle thousands of conversations simultaneously without hiring a single support agent."
        painPoint="Customers message at midnight. Leads ask questions on weekends. Your team can't be online 24/7, so you lose business while you sleep."
        showContactForm={true}
        formTitle="Get Free Strategy Session"
      />

      <ServiceBenefits title="What AI Chatbots Do" benefits={benefits} />

      <ServiceHowItWorks steps={steps} />

      <section className="container mx-auto px-6 py-20 bg-muted/30">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">Chatbot Capabilities</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {capabilities.map((capability, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
              >
                <h3 className="text-xl font-semibold mb-4">{capability.title}</h3>
                <ul className="space-y-2">
                  {capability.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-2 text-sm text-muted-foreground">
                      <span className="text-accent mt-1">•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="mb-6 text-3xl font-bold lg:text-4xl">AI Technologies We Use</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              "OpenAI GPT",
              "LangChain",
              "Rasa",
              "Dialogflow",
              "Custom NLP Models",
              "Vector Databases",
              "Sentiment Analysis",
              "Multi-language AI",
            ].map((tech, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-4 text-sm font-medium hover:border-accent/50 transition-colors"
              >
                {tech}
              </div>
            ))}
          </div>
        </div>
      </section>

      <ServiceFAQ faqs={faqs} />

      <ServiceCTA
        title="Ready to Build Your AI Chatbot?"
        description="Book a free chatbot strategy session. We'll review your support/sales process, identify automation opportunities, and show you what an AI chatbot could handle for your business."
        primaryCTA="Get Free Strategy Session"
        secondaryCTA="See Other Services"
        secondaryHref="/services"
      />

      <Footer />
    </main>
  )
}
