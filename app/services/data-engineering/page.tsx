import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { ServiceHero } from "@/components/service-hero"
import { ServiceBenefits } from "@/components/service-benefits"
import { ServiceHowItWorks } from "@/components/service-how-it-works"
import { ServiceFAQ } from "@/components/service-faq"
import { ServiceCTA } from "@/components/service-cta"

export const metadata = {
  title: "Data Engineering Services | Data Pipelines & Warehousing | Nexveria",
  description:
    "Build reliable data infrastructure. Automated pipelines, cloud data warehouses, and real-time analytics that turn raw data into business intelligence.",
}

export default function DataEngineeringPage() {
  const benefits = [
    {
      title: "Access All Your Data in One Place",
      description:
        "Data scattered across 10 systems makes analysis impossible. We build centralized data warehouses that pull from every source automatically. Query everything in one dashboard.",
      metric: "Single Source",
    },
    {
      title: "Real-Time Insights, Not Monthly Reports",
      description:
        "Stop waiting weeks for reports. Automated pipelines update data every hour or minute. Your dashboards show current reality, not last month's stale numbers.",
      metric: "Live Data",
    },
    {
      title: "Scale Without Breaking",
      description:
        "Manual data processes break as volume grows. Automated pipelines handle millions of records daily. From startup to enterprise, your data infrastructure scales seamlessly.",
      metric: "10M+ Records/day",
    },
  ]

  const steps = [
    {
      number: "1",
      title: "Data Audit & Architecture Design",
      description:
        "Map all your data sources: databases, APIs, spreadsheets, third-party tools. Identify what data matters for decisions. Design scalable architecture that brings it together efficiently.",
    },
    {
      number: "2",
      title: "Pipeline Development & Integration",
      description:
        "Build automated pipelines that extract, transform, and load data. Handle failures gracefully, validate quality, and alert on issues. Connect all systems so data flows automatically.",
    },
    {
      number: "3",
      title: "Warehouse Setup & Optimization",
      description:
        "Deploy cloud data warehouse (Snowflake, BigQuery, or Redshift). Structure data for fast queries. Implement access controls and compliance. Train your team to query and analyze independently.",
    },
  ]

  const services = [
    {
      title: "Data Pipeline Development",
      items: [
        "ETL/ELT pipeline automation",
        "Real-time streaming data",
        "Batch processing at scale",
        "Error handling and monitoring",
        "Data quality validation",
      ],
    },
    {
      title: "Data Warehousing",
      items: [
        "Cloud warehouse setup (Snowflake, BigQuery, Redshift)",
        "Data modeling and schema design",
        "Performance optimization",
        "Historical data storage",
        "Query optimization",
      ],
    },
    {
      title: "Data Integration",
      items: [
        "API integrations",
        "Database replication",
        "Third-party tool connections",
        "Legacy system data extraction",
        "File-based data ingestion",
      ],
    },
    {
      title: "Data Governance",
      items: [
        "Access control and security",
        "Data lineage tracking",
        "Compliance and auditing",
        "Data cataloging",
        "Master data management",
      ],
    },
  ]

  const faqs = [
    {
      question: "We have data in 15 different systems. Can you consolidate it?",
      answer:
        "Yes, that's what we specialize in. We've consolidated data from CRMs, ERPs, databases, spreadsheets, and SaaS tools. Each source gets a pipeline that extracts data automatically. Everything lands in your centralized warehouse, ready to query.",
    },
    {
      question: "How do we handle sensitive customer data?",
      answer:
        "Security and compliance are built in. We encrypt data in transit and at rest, implement role-based access, and maintain audit logs. For GDPR or other regulations, we handle anonymization, retention policies, and deletion requests automatically.",
    },
    {
      question: "What if a data pipeline fails?",
      answer:
        "Failures are inevitable, handling them isn't. We implement retry logic, backup strategies, and immediate alerts. Most failures auto-recover. Critical issues trigger notifications so your team can respond. No silent failures, no data loss.",
    },
    {
      question: "Can non-technical people access the data warehouse?",
      answer:
        "Yes. We connect BI tools like Tableau, Power BI, or Looker to the warehouse. Business users query through familiar interfaces without writing SQL. For advanced users, we provide SQL access with clear documentation.",
    },
    {
      question: "How much does data infrastructure cost?",
      answer:
        "Cloud warehouses cost KES 100,000-500,000/month depending on data volume. Pipeline tools add KES 50,000-200,000/month. Sounds expensive, but consider the cost of bad decisions from incomplete data. ROI usually clear within 3-6 months.",
    },
    {
      question: "Our data is messy. Can you clean it?",
      answer:
        "Data cleaning is part of pipeline development. We standardize formats, remove duplicates, validate accuracy, and handle missing values. The warehouse gets clean, reliable data. Garbage in, quality out.",
    },
    {
      question: "Can we start small and scale?",
      answer:
        "Absolutely. Start with critical data sources and priority use cases. Prove value quickly. Then expand to more sources and users. Most clients start with 3-5 data sources and grow to 15-20 over time.",
    },
  ]

  return (
    <main className="min-h-screen">
      <Navbar />
      <ServiceHero
        subtitle="Data Engineering"
        title="Your Data Is Scattered, Stale, and Useless"
        description="Build automated data infrastructure that brings everything together. Real-time pipelines, centralized warehouses, and reliable analytics that actually drive decisions."
        painPoint="Data lives in 10 different systems. Reports take weeks to generate. By the time you have answers, the questions have changed."
        showContactForm={true}
        formTitle="Get Free Data Audit"
      />

      <ServiceBenefits title="What Data Engineering Solves" benefits={benefits} />

      <ServiceHowItWorks steps={steps} />

      <section className="container mx-auto px-6 py-20 bg-muted/30">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">Data Services We Provide</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
              >
                <h3 className="text-xl font-semibold mb-4">{service.title}</h3>
                <ul className="space-y-2">
                  {service.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-2 text-sm text-muted-foreground">
                      <span className="text-accent mt-1">•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="mb-6 text-3xl font-bold lg:text-4xl">Modern Data Stack</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              "Apache Airflow",
              "dbt",
              "Snowflake",
              "BigQuery",
              "Redshift",
              "PostgreSQL",
              "Kafka",
              "Spark",
            ].map((tech, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-4 text-sm font-medium hover:border-accent/50 transition-colors"
              >
                {tech}
              </div>
            ))}
          </div>
        </div>
      </section>

      <ServiceFAQ faqs={faqs} />

      <ServiceCTA
        title="Ready to Fix Your Data Infrastructure?"
        description="Book a free data audit. We'll map your data sources, identify bottlenecks, and show you how to build reliable data infrastructure that scales."
        primaryCTA="Get Free Data Audit"
        secondaryCTA="See Other Services"
        secondaryHref="/services"
      />

      <Footer />
    </main>
  )
}
