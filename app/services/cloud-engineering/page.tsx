import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { ServiceHero } from "@/components/service-hero"
import { ServiceBenefits } from "@/components/service-benefits"
import { ServiceHowItWorks } from "@/components/service-how-it-works"
import { ServiceFAQ } from "@/components/service-faq"
import { ServiceCTA } from "@/components/service-cta"
import { StatsSection } from "@/components/stats-section"
import { ContactSection } from "@/components/contact-section"

export const metadata = {
  title: "Cloud Engineering Services | AWS, Azure & GCP Infrastructure | Nexveria",
  description:
    "Scale faster, pay less, never go down. Professional cloud infrastructure on AWS, Azure, and Google Cloud with migration, optimization, and 24/7 support.",
}

export default function CloudEngineeringPage() {
  const benefits = [
    {
      title: "Cut Infrastructure Costs by 40-60%",
      description:
        "Most businesses overpay for cloud resources. We analyze usage patterns, right-size instances, implement auto-scaling, and eliminate waste. Clients typically save 40-60% on cloud bills within 3 months.",
      metric: "40-60% Savings",
    },
    {
      title: "Scale Without Crashing",
      description:
        "Traffic spikes shouldn't take down your business. We build infrastructure that auto-scales under load, handles millions of requests, and maintains 99.9% uptime. Your system grows with demand.",
      metric: "99.9% Uptime",
    },
    {
      title: "Stop Worrying About Servers",
      description:
        "Your team shouldn't manage infrastructure manually. We implement Infrastructure as Code, automated deployments, and monitoring. Focus on building products, not fighting server issues.",
      metric: "Zero Manual Ops",
    },
  ]

  const steps = [
    {
      number: "1",
      title: "Infrastructure Audit & Strategy",
      description:
        "We analyze your current setup: what you're running, what it costs, where the bottlenecks are. Then design cloud architecture optimized for your workload. You see exactly what we'll build and why before we start.",
    },
    {
      number: "2",
      title: "Migration or Build",
      description:
        "Migrating from on-premise? We move workloads with zero downtime. Building new? We provision infrastructure using Terraform or CloudFormation. Everything is automated, documented, and reproducible.",
    },
    {
      number: "3",
      title: "Optimization & Support",
      description:
        "Post-launch, we monitor performance, optimize costs, and handle incidents. 24/7 monitoring alerts us before problems impact users. Monthly reviews identify further optimization opportunities.",
    },
  ]

  const services = [
    {
      title: "Cloud Migration",
      items: [
        "AWS, Azure, GCP migration",
        "Zero-downtime data transfer",
        "Application refactoring for cloud",
        "Legacy system modernization",
        "Hybrid cloud setup",
      ],
    },
    {
      title: "Infrastructure Setup",
      items: [
        "Auto-scaling architecture",
        "Load balancing & CDN",
        "Database optimization",
        "Serverless implementations",
        "Multi-region deployment",
      ],
    },
    {
      title: "Cost Optimization",
      items: [
        "Resource right-sizing",
        "Reserved instance planning",
        "Spot instance strategies",
        "Automated shutdown policies",
        "Cost monitoring dashboards",
      ],
    },
    {
      title: "Security & Compliance",
      items: [
        "IAM and access control",
        "Network security (VPC, firewalls)",
        "Data encryption at rest/transit",
        "Compliance (GDPR, SOC 2)",
        "Security audits and monitoring",
      ],
    },
  ]

  const faqs = [
    {
      question: "Which cloud provider should we use: AWS, Azure, or Google Cloud?",
      answer:
        "Depends on your existing stack and needs. AWS has the most services and maturity. Azure if you're Microsoft-heavy (Active Directory, .NET). Google Cloud for data/ML workloads. We recommend based on your requirements, not vendor relationships.",
    },
    {
      question: "Can you migrate our on-premise infrastructure without downtime?",
      answer:
        "Yes. We use phased migration: set up cloud infrastructure, replicate data in real-time, test thoroughly, then switch DNS. Users experience no downtime. Rollback plan ready if issues arise.",
    },
    {
      question: "How much does cloud infrastructure cost?",
      answer:
        "Varies widely. Small app: KES 50,000-150,000/month. Medium business: KES 200,000-500,000/month. Enterprise: millions. We provide detailed cost estimates before migration. Optimization typically pays for itself in savings.",
    },
    {
      question: "What if our cloud costs spiral out of control?",
      answer:
        "We implement cost controls from day one: budgets, alerts, auto-shutdown of dev environments, and monthly reviews. You're notified if spending exceeds thresholds. Most cost overruns come from unmonitored resources, which we prevent.",
    },
    {
      question: "Do we need a dedicated DevOps team for cloud infrastructure?",
      answer:
        "Not necessarily. We can manage your infrastructure as a service (monthly retainer) or train your team to handle it. Small teams often prefer managed services; larger companies want internal capability. We support both models.",
    },
    {
      question: "Can we scale globally with cloud?",
      answer:
        "Absolutely. We deploy multi-region architecture so your app runs close to users worldwide. Deploy in Kenya, Europe, and US simultaneously. Improves performance and provides redundancy if one region fails.",
    },
    {
      question: "What happens if AWS/Azure/GCP goes down?",
      answer:
        "Cloud providers have 99.99% SLA, but outages happen. We design for this: multi-region deployments, backup providers, graceful degradation. Critical systems stay online even if one cloud provider has issues.",
    },
  ]

  return (
    <main className="min-h-screen">
      <Navbar />
      <ServiceHero
        subtitle="Cloud Engineering"
        title="Your Cloud Bill Is Too High and Your Servers Still Crash"
        description="Migrate to the cloud properly. Auto-scaling infrastructure, 60% cost savings, and 99.9% uptime on AWS, Azure, or Google Cloud."
        painPoint="You're paying for servers you don't use, scaling manually when traffic spikes, and losing customers when systems go down."
        showContactForm={true}
        formTitle="Get Free Cloud Audit"
      />

      <StatsSection />

      <ServiceBenefits title="What Proper Cloud Engineering Delivers" benefits={benefits} />

      <ServiceHowItWorks steps={steps} />

      <section className="container mx-auto px-6 py-20 bg-muted/30">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">Cloud Services We Provide</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
              >
                <h3 className="text-xl font-semibold mb-4">{service.title}</h3>
                <ul className="space-y-2">
                  {service.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-2 text-sm text-muted-foreground">
                      <span className="text-accent mt-1">•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="mb-6 text-3xl font-bold lg:text-4xl">Cloud Platforms We Master</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {[
              "AWS",
              "Microsoft Azure",
              "Google Cloud Platform",
              "Digital Ocean",
              "Terraform",
              "Kubernetes",
            ].map((platform, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-4 text-sm font-medium hover:border-accent/50 transition-colors"
              >
                {platform}
              </div>
            ))}
          </div>
        </div>
      </section>

      <ServiceFAQ faqs={faqs} />

      <ContactSection />

      <ServiceCTA
        title="Ready to Fix Your Cloud Infrastructure?"
        description="Book a free cloud audit. We'll review your current setup, identify cost savings, and show you how to build infrastructure that scales."
        primaryCTA="Get Free Cloud Audit"
        primaryHref="#contact"
        secondaryCTA="See Other Services"
        secondaryHref="/services"
      />

      <Footer />
    </main>
  )
}
