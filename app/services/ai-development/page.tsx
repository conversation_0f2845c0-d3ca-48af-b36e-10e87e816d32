import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { ServiceHero } from "@/components/service-hero"
import { ServiceBenefits } from "@/components/service-benefits"
import { ServiceHowItWorks } from "@/components/service-how-it-works"
import { ServiceFAQ } from "@/components/service-faq"
import { ServiceCTA } from "@/components/service-cta"

export const metadata = {
  title: "AI Development Services | Machine Learning Solutions | Nexveria",
  description:
    "Build AI solutions that solve real business problems. From predictive analytics to automated decision-making, we turn data into competitive advantage.",
}

export default function AIDevelopmentPage() {
  const benefits = [
    {
      title: "Automate What Humans Can't Scale",
      description:
        "AI handles thousands of decisions per second. Fraud detection, demand forecasting, content moderation – tasks that would require armies of people. Your business scales without scaling headcount.",
      metric: "1000x Scale",
    },
    {
      title: "Make Better Decisions Faster",
      description:
        "AI analyzes patterns humans miss. Predict customer churn before it happens. Optimize inventory based on dozens of factors. Make data-driven decisions in real-time, not quarterly meetings.",
      metric: "95% Accuracy",
    },
    {
      title: "Turn Data into Revenue",
      description:
        "Your business generates data daily. AI converts it to insights that drive action. Personalized recommendations increase sales by 30%. Predictive maintenance cuts costs by 40%.",
      metric: "30-40% Gains",
    },
  ]

  const steps = [
    {
      number: "1",
      title: "Problem Definition & Feasibility",
      description:
        "Not every problem needs AI. We identify if AI is the right solution or if traditional software works better. Define success metrics, assess data quality, and validate that AI will deliver ROI before building anything.",
    },
    {
      number: "2",
      title: "Proof of Concept Development",
      description:
        "Build a small AI model with your real data. Test the approach, validate accuracy, and prove the concept works. This de-risks the project before major investment. Most POCs take 4-8 weeks and cost a fraction of full implementation.",
    },
    {
      number: "3",
      title: "Production Deployment & Optimization",
      description:
        "Scale the working model to production. Integrate with your systems, handle edge cases, and monitor performance. AI models improve over time with more data. We continuously retrain and optimize for better results.",
    },
  ]

  const useCases = [
    {
      title: "Predictive Analytics",
      items: [
        "Customer churn prediction",
        "Demand forecasting",
        "Price optimization",
        "Sales forecasting",
        "Risk assessment",
      ],
    },
    {
      title: "Computer Vision",
      items: [
        "Product quality inspection",
        "Facial recognition",
        "Document processing (OCR)",
        "Object detection and tracking",
        "Image classification",
      ],
    },
    {
      title: "Natural Language Processing",
      items: [
        "Sentiment analysis",
        "Text classification",
        "Named entity recognition",
        "Language translation",
        "Content summarization",
      ],
    },
    {
      title: "Recommendation Systems",
      items: [
        "Product recommendations",
        "Content personalization",
        "Dynamic pricing",
        "Next-best-action suggestions",
        "Customer matching",
      ],
    },
  ]

  const faqs = [
    {
      question: "Do we need a data scientist on our team to use AI?",
      answer:
        "No. We build, deploy, and maintain the AI models. Your team uses them like any other software feature. We provide simple APIs or dashboards. Training is minimal because the interface is designed for business users, not data scientists.",
    },
    {
      question: "How much data do we need for AI to work?",
      answer:
        "It depends. Some models need thousands of examples, others work with hundreds. We assess your data during feasibility. If you don't have enough, we discuss options: synthetic data, transfer learning, or starting with simpler approaches until you collect more data.",
    },
    {
      question: "Can AI really be 95% accurate?",
      answer:
        "For some tasks, yes. Image recognition, text classification, and pattern detection regularly hit 95%+ accuracy. Other problems are harder. We're transparent about expected accuracy during POC. If the model isn't accurate enough to be useful, we don't deploy it.",
    },
    {
      question: "What if the AI makes wrong decisions?",
      answer:
        "AI augments humans, doesn't replace them for critical decisions. We implement confidence thresholds: high-confidence predictions are automated, low-confidence goes to humans. You control the automation level based on risk tolerance. All AI decisions are logged and auditable.",
    },
    {
      question: "How long does it take to build an AI solution?",
      answer:
        "POC: 4-8 weeks. Production-ready model: 3-6 months. Complex systems like recommendation engines or multi-model solutions: 6-12 months. We deliver incrementally, so you see working AI quickly, then we refine and scale.",
    },
    {
      question: "Is AI just a buzzword or does it actually work?",
      answer:
        "It works for specific, well-defined problems with good data. Fraud detection, demand forecasting, image recognition – proven use cases. But AI isn't magic. If someone promises general artificial intelligence or 100% accuracy, they're lying. We focus on practical AI that delivers measurable business value.",
    },
    {
      question: "What happens when the AI model degrades over time?",
      answer:
        "All AI models degrade as data patterns change. We implement monitoring to detect performance drops. When accuracy falls below threshold, we retrain with fresh data. This is part of our managed AI service – we keep models performing at peak.",
    },
  ]

  return (
    <main className="min-h-screen">
      <Navbar />
      <ServiceHero
        subtitle="AI Development"
        title="Stop Talking About AI, Start Using It"
        description="Build AI solutions that solve real problems: automate decisions, predict outcomes, and unlock insights from your data. Practical AI that drives revenue, not just hype."
        painPoint="Everyone talks about AI, but nobody shows you how to actually use it for your business. Meanwhile, competitors are using AI to eat your lunch."
        showContactForm={true}
        formTitle="Get Free AI Assessment"
      />

      <ServiceBenefits title="What AI Actually Does for Business" benefits={benefits} />

      <ServiceHowItWorks steps={steps} />

      <section className="container mx-auto px-6 py-20 bg-muted/30">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">AI Use Cases We've Built</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {useCases.map((useCase, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
              >
                <h3 className="text-xl font-semibold mb-4">{useCase.title}</h3>
                <ul className="space-y-2">
                  {useCase.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start gap-2 text-sm text-muted-foreground">
                      <span className="text-accent mt-1">•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="mb-6 text-3xl font-bold lg:text-4xl">Our AI Technology Stack</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {[
              "TensorFlow",
              "PyTorch",
              "Scikit-learn",
              "OpenAI GPT",
              "Hugging Face",
              "LangChain",
              "Computer Vision",
              "NLP Models",
            ].map((tech, index) => (
              <div
                key={index}
                className="rounded-lg border border-border bg-card p-4 text-sm font-medium hover:border-accent/50 transition-colors"
              >
                {tech}
              </div>
            ))}
          </div>
        </div>
      </section>

      <ServiceFAQ faqs={faqs} />

      <ServiceCTA
        title="Ready to Build Real AI Solutions?"
        description="Book a free AI feasibility assessment. We'll analyze your problem, assess your data, and tell you honestly if AI is the right solution. No hype, just practical engineering advice."
        primaryCTA="Get Free Assessment"
        secondaryCTA="See Other Services"
        secondaryHref="/services"
      />

      <Footer />
    </main>
  )
}
