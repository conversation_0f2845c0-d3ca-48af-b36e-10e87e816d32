import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Code, Cloud, RefreshCw, Cog, Brain, MessageSquare as ChatBot, Database, BarChart, MessageSquare, Share2, Target, TrendingUp, Zap } from "lucide-react"

export const metadata = {
  title: "Software Development & Marketing Services | Nexveria",
  description:
    "Custom software development, AI solutions, cloud engineering, and marketing services that drive growth for Kenyan businesses.",
}

export default function ServicesPage() {
  const engineeringServices = [
    {
      icon: Code,
      title: "Custom Software Development",
      description: "Bespoke applications tailored to your exact business needs",
      tags: ["Web Apps", "Mobile Apps", "AI Solutions", "Cloud Infrastructure"],
      href: "/services/custom-software",
      gradient: "from-blue-500/20 to-cyan-500/20",
    },
    {
      icon: Cloud,
      title: "Cloud Engineering",
      description: "AWS, Azure, and Google Cloud infrastructure",
      tags: ["Migration", "Optimization", "Cost Savings", "24/7 Uptime"],
      href: "/services/cloud-engineering",
      gradient: "from-purple-500/20 to-pink-500/20",
    },
    {
      icon: RefreshCw,
      title: "Application Modernization",
      description: "Upgrade legacy systems to modern technology",
      tags: ["60% Cost Cut", "10x Faster", "Modern Stack", "Zero Downtime"],
      href: "/services/application-modernization",
      gradient: "from-orange-500/20 to-red-500/20",
    },
    {
      icon: Cog,
      title: "DevOps & Automation",
      description: "CI/CD pipelines and deployment automation",
      tags: ["Deploy 10x Faster", "Auto-Testing", "Infrastructure as Code", "Monitoring"],
      href: "/services/devops",
      gradient: "from-green-500/20 to-emerald-500/20",
    },
  ]

  const dataAIServices = [
    {
      icon: Brain,
      title: "AI Development",
      description: "Machine learning solutions that automate decisions",
      tags: ["Predictive Analytics", "Computer Vision", "NLP", "95% Accuracy"],
      href: "/services/ai-development",
      gradient: "from-violet-500/20 to-purple-500/20",
    },
    {
      icon: ChatBot,
      title: "AI Chatbot Development",
      description: "Intelligent chatbots for customer service and sales",
      tags: ["24/7 Support", "80% Automated", "Multi-Channel", "Smart Routing"],
      href: "/services/ai-chatbot",
      gradient: "from-indigo-500/20 to-blue-500/20",
    },
    {
      icon: Database,
      title: "Data Engineering",
      description: "Data pipelines, warehousing, and ETL",
      tags: ["Real-Time Data", "Single Source", "10M Records/day", "Auto Pipelines"],
      href: "/services/data-engineering",
      gradient: "from-cyan-500/20 to-teal-500/20",
    },
    {
      icon: BarChart,
      title: "Business Intelligence",
      description: "Live dashboards and automated reporting",
      tags: ["Real-Time Insights", "Custom Dashboards", "Auto Reports", "Smart Alerts"],
      href: "/services/business-intelligence",
      gradient: "from-teal-500/20 to-green-500/20",
    },
  ]

  const marketingServices = [
    {
      icon: Zap,
      title: "Task Automation Service",
      description: "AI assistant for social media, emails & repetitive tasks",
      tags: ["Save 20+ hrs/week", "AI Assistant", "Multi-Platform", "From KES 25K/mo"],
      href: "/services/task-automation",
      gradient: "from-violet-500/20 to-purple-500/20",
    },
    {
      icon: MessageSquare,
      title: "WhatsApp Automation",
      description: "24/7 automated responses and lead capture",
      tags: ["Never Miss Leads", "24/7 Active", "Auto Replies", "From KES 15K/mo"],
      href: "/services/whatsapp-automation",
      gradient: "from-green-500/20 to-lime-500/20",
    },
    {
      icon: Share2,
      title: "Social Media Management",
      description: "Done-for-you content that drives customers",
      tags: ["Daily Posting", "Custom Graphics", "Full Engagement", "From KES 25K/mo"],
      href: "/services/social-media-management",
      gradient: "from-pink-500/20 to-rose-500/20",
    },
    {
      icon: Target,
      title: "Google Ads",
      description: "Show up when customers search for what you sell",
      tags: ["Top of Google", "Pay Per Click", "Qualified Leads", "From KES 20K/mo"],
      href: "/services/google-ads",
      gradient: "from-yellow-500/20 to-orange-500/20",
    },
    {
      icon: TrendingUp,
      title: "Facebook & Instagram Ads",
      description: "Targeted campaigns that stop the scroll",
      tags: ["Laser Targeting", "Stop the Scroll", "Retargeting", "From KES 18K/mo"],
      href: "/services/facebook-ads",
      gradient: "from-blue-500/20 to-indigo-500/20",
    },
  ]

  return (
    <main className="min-h-screen">
      <Navbar />

      {/* Hero Section */}
      <section className="container mx-auto px-6 pt-32 pb-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-6 inline-block">
            <span className="text-sm font-medium text-accent">Our Services</span>
          </div>
          <h1 className="mb-6 text-5xl font-bold leading-tight text-balance lg:text-6xl">
            Software Development & Growth Services
          </h1>
          <p className="mb-4 text-xl text-muted-foreground leading-relaxed text-pretty">
            From building custom software to driving customers to your business. Complete technology and growth solutions under one roof.
          </p>
        </div>
      </section>

      {/* Engineering Services */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold lg:text-4xl">Engineering Services</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {engineeringServices.map((service, index) => {
              const Icon = service.icon
              return (
                <Link
                  key={index}
                  href={service.href}
                  className="group rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
                >
                  <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-accent/10">
                    <Icon className="h-6 w-6 text-accent" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2 group-hover:text-accent transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-sm text-muted-foreground">{service.description}</p>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* Data & AI Services */}
      <section className="container mx-auto px-6 py-20 bg-muted/30">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold lg:text-4xl">Data & AI Services</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {dataAIServices.map((service, index) => {
              const Icon = service.icon
              return (
                <Link
                  key={index}
                  href={service.href}
                  className="group rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
                >
                  <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-accent/10">
                    <Icon className="h-6 w-6 text-accent" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2 group-hover:text-accent transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-sm text-muted-foreground">{service.description}</p>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* Marketing Services */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold lg:text-4xl">Marketing & Growth Services</h2>
          <div className="grid md:grid-cols-2 gap-8">
            {marketingServices.map((service, index) => {
              const Icon = service.icon
              return (
                <Link
                  key={index}
                  href={service.href}
                  className="group rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
                >
                  <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-accent/10">
                    <Icon className="h-6 w-6 text-accent" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2 group-hover:text-accent transition-colors">
                    {service.title}
                  </h3>
                  <p className="text-sm text-muted-foreground">{service.description}</p>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="container mx-auto px-6 py-20 bg-muted/30">
        <div className="max-w-4xl mx-auto">
          <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">Why Work With Us?</h2>
          <div className="grid gap-8 md:grid-cols-2">
            <div className="rounded-lg border border-border bg-card p-6">
              <h3 className="mb-3 text-xl font-semibold">Full-Stack Capability</h3>
              <p className="text-muted-foreground leading-relaxed">
                Need software built? We do that. Need customers? We do that too. One partner for your entire technology and growth journey.
              </p>
            </div>
            <div className="rounded-lg border border-border bg-card p-6">
              <h3 className="mb-3 text-xl font-semibold">Kenya-First Approach</h3>
              <p className="text-muted-foreground leading-relaxed">
                We understand the Kenyan market, M-Pesa integration, mobile-first users, and local business challenges. No copy-paste solutions.
              </p>
            </div>
            <div className="rounded-lg border border-border bg-card p-6">
              <h3 className="mb-3 text-xl font-semibold">Technical Excellence</h3>
              <p className="text-muted-foreground leading-relaxed">
                Certified engineers, proven methodologies, and modern technology stacks. We build software that scales and lasts.
              </p>
            </div>
            <div className="rounded-lg border border-border bg-card p-6">
              <h3 className="mb-3 text-xl font-semibold">Transparent & Reliable</h3>
              <p className="text-muted-foreground leading-relaxed">
                No hidden fees. No surprise charges. We deliver what we promise, on time and on budget. That's how we've built our reputation.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto rounded-2xl border border-accent/20 bg-gradient-to-br from-accent/5 to-transparent p-12 text-center">
          <h2 className="mb-4 text-3xl font-bold lg:text-4xl">Not Sure Which Service You Need?</h2>
          <p className="mb-8 text-lg text-muted-foreground leading-relaxed">
            Book a free consultation. We'll discuss your challenges, recommend the right solutions, and provide honest technical advice. No sales pressure.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              asChild
              size="lg"
              className="rounded-full bg-accent text-accent-foreground hover:bg-accent/90 px-8"
            >
              <Link href="/#contact">Book Free Consultation</Link>
            </Button>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="rounded-full border-2 border-accent bg-transparent hover:bg-accent/10"
            >
              <Link href="/blog">Read Our Blog</Link>
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
