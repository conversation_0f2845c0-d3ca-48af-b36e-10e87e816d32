@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(0.98 0 0);
  --foreground: oklch(0.15 0.02 240);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 240);
  --primary: oklch(0.45 0.20 220);
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.92 0.01 240);
  --secondary-foreground: oklch(0.15 0.02 240);
  --muted: oklch(0.92 0.01 240);
  --muted-foreground: oklch(0.45 0.05 240);
  --accent: oklch(0.50 0.22 210);
  --accent-foreground: oklch(0.98 0 0);
  --destructive: oklch(0.55 0.25 20);
  --destructive-foreground: oklch(0.98 0 0);
  --border: oklch(0.85 0.01 240);
  --input: oklch(0.92 0.01 240);
  --ring: oklch(0.45 0.20 220);
  --chart-1: oklch(0.45 0.20 220);
  --chart-2: oklch(0.50 0.22 210);
  --chart-3: oklch(0.40 0.18 200);
  --chart-4: oklch(0.35 0.16 230);
  --chart-5: oklch(0.55 0.15 195);
  --radius: 0.625rem;
  --sidebar: oklch(0.98 0 0);
  --sidebar-foreground: oklch(0.15 0.02 240);
  --sidebar-primary: oklch(0.45 0.20 220);
  --sidebar-primary-foreground: oklch(0.98 0 0);
  --sidebar-accent: oklch(0.92 0.01 240);
  --sidebar-accent-foreground: oklch(0.15 0.02 240);
  --sidebar-border: oklch(0.85 0.01 240);
  --sidebar-ring: oklch(0.45 0.20 220);
}

.dark {
  --background: oklch(0.12 0.015 250);
  --foreground: oklch(0.95 0.005 250);
  --card: oklch(0.15 0.02 250);
  --card-foreground: oklch(0.95 0.005 250);
  --popover: oklch(0.15 0.02 250);
  --popover-foreground: oklch(0.95 0.005 250);
  --primary: oklch(0.65 0.20 220);
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.20 0.025 250);
  --secondary-foreground: oklch(0.95 0.005 250);
  --muted: oklch(0.20 0.025 250);
  --muted-foreground: oklch(0.60 0.015 250);
  --accent: oklch(0.55 0.18 210);
  --accent-foreground: oklch(0.98 0 0);
  --destructive: oklch(0.55 0.22 25);
  --destructive-foreground: oklch(0.98 0 0);
  --border: oklch(0.25 0.03 250);
  --input: oklch(0.18 0.025 250);
  --ring: oklch(0.65 0.20 220);
  --chart-1: oklch(0.65 0.20 220);
  --chart-2: oklch(0.55 0.18 210);
  --chart-3: oklch(0.60 0.16 200);
  --chart-4: oklch(0.50 0.15 230);
  --chart-5: oklch(0.70 0.14 195);
  --sidebar: oklch(0.15 0.02 250);
  --sidebar-foreground: oklch(0.95 0.005 250);
  --sidebar-primary: oklch(0.65 0.20 220);
  --sidebar-primary-foreground: oklch(0.98 0 0);
  --sidebar-accent: oklch(0.20 0.025 250);
  --sidebar-accent-foreground: oklch(0.95 0.005 250);
  --sidebar-border: oklch(0.25 0.03 250);
  --sidebar-ring: oklch(0.65 0.20 220);
}

@theme inline {
  /* optional: --font-sans, --font-serif, --font-mono if they are applied in the layout.tsx */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
