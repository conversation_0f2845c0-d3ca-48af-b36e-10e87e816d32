import { NextRequest, NextResponse } from "next/server"
import { Resend } from "resend"
import { z } from "zod"

const contactFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  company: z.string().min(1, "Company name is required"),
  phone: z.string().min(1, "Phone number is required"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

const resend = new Resend(process.env.RESEND_API_KEY)

export async function POST(request: NextRequest) {
  try {
    console.log("=== Contact Form API Route Called ===")
    const body = await request.json()
    console.log("Request body:", body)

    // Validate the request body
    const validatedData = contactFormSchema.parse(body)
    console.log("Validation successful:", validatedData)

    console.log("Attempting to send email via Resend...")
    // Send email using Resend
    const { data, error } = await resend.emails.send({
      from: "Nexveria Contact Form <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: `New Contact Form Submission from ${validatedData.firstName} ${validatedData.lastName}`,
      html: `
        <!DOCTYPE html>
        <html>
          <head>
            <meta charset="utf-8">
            <style>
              body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
              }
              .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
              }
              .header {
                background-color: #4f46e5;
                color: white;
                padding: 20px;
                border-radius: 8px 8px 0 0;
              }
              .content {
                background-color: #f9fafb;
                padding: 30px;
                border-radius: 0 0 8px 8px;
              }
              .field {
                margin-bottom: 20px;
              }
              .label {
                font-weight: bold;
                color: #4f46e5;
                display: block;
                margin-bottom: 5px;
              }
              .value {
                color: #1f2937;
              }
              .message-box {
                background-color: white;
                padding: 15px;
                border-radius: 5px;
                border-left: 4px solid #4f46e5;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h2 style="margin: 0;">New Contact Form Submission</h2>
              </div>
              <div class="content">
                <div class="field">
                  <span class="label">Name:</span>
                  <span class="value">${validatedData.firstName} ${validatedData.lastName}</span>
                </div>

                <div class="field">
                  <span class="label">Email:</span>
                  <span class="value">${validatedData.email}</span>
                </div>

                <div class="field">
                  <span class="label">Company:</span>
                  <span class="value">${validatedData.company}</span>
                </div>

                <div class="field">
                  <span class="label">Phone:</span>
                  <span class="value">${validatedData.phone}</span>
                </div>

                <div class="field">
                  <span class="label">Message:</span>
                  <div class="message-box">
                    ${validatedData.message.replace(/\n/g, '<br>')}
                  </div>
                </div>
              </div>
            </div>
          </body>
        </html>
      `,
    })

    if (error) {
      console.error("❌ Resend error:", error)
      return NextResponse.json(
        { error: "Failed to send email" },
        { status: 500 }
      )
    }

    console.log("✅ Email sent successfully! ID:", data?.id)
    return NextResponse.json(
      { message: "Email sent successfully", id: data?.id },
      { status: 200 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid form data", details: error.errors },
        { status: 400 }
      )
    }

    console.error("Error processing contact form:", error)
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
