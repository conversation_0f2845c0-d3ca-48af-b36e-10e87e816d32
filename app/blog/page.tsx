import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { BlogCard } from "@/components/blog-card"
import { blogPosts, getAllCategories } from "@/lib/blog-posts"

export const metadata = {
  title: "Marketing Tips for Kenyan Businesses | Nexveria Blog",
  description:
    "Learn how to get more customers through WhatsApp, social media, and online advertising. Practical marketing advice for Kenyan small businesses.",
}

export default function BlogPage() {
  const categories = getAllCategories()
  const featuredPosts = blogPosts.filter((post) => post.featured)
  const recentPosts = blogPosts.filter((post) => !post.featured)

  return (
    <main className="min-h-screen">
      <Navbar />

      {/* Hero Section */}
      <section className="container mx-auto px-6 pt-32 pb-20">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-6 inline-block">
            <span className="text-sm font-medium text-accent">Our Blog</span>
          </div>
          <h1 className="mb-6 text-5xl font-bold leading-tight text-balance lg:text-6xl">
            Marketing Tips That Actually Work in Kenya
          </h1>
          <p className="text-xl text-muted-foreground leading-relaxed text-pretty">
            No theory. No fluff. Just practical advice from helping hundreds of Kenyan businesses get more
            customers.
          </p>
        </div>
      </section>

      {/* Categories */}
      <section className="container mx-auto px-6 pb-12">
        <div className="flex flex-wrap justify-center gap-3">
          {categories.map((category) => (
            <button
              key={category}
              className="rounded-full border border-border bg-card px-4 py-2 text-sm font-medium hover:border-accent/50 hover:bg-accent/10 transition-colors"
            >
              {category}
            </button>
          ))}
        </div>
      </section>

      {/* Featured Posts */}
      {featuredPosts.length > 0 && (
        <section className="container mx-auto px-6 pb-20">
          <h2 className="mb-8 text-2xl font-bold">Featured Articles</h2>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {featuredPosts.map((post) => (
              <BlogCard key={post.slug} post={post} />
            ))}
          </div>
        </section>
      )}

      {/* Recent Posts */}
      {recentPosts.length > 0 && (
        <section className="container mx-auto px-6 pb-20">
          <h2 className="mb-8 text-2xl font-bold">Recent Articles</h2>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {recentPosts.map((post) => (
              <BlogCard key={post.slug} post={post} />
            ))}
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="container mx-auto px-6 py-20 bg-muted/30">
        <div className="max-w-4xl mx-auto rounded-2xl border border-accent/20 bg-gradient-to-br from-accent/5 to-transparent p-12 text-center">
          <h2 className="mb-4 text-3xl font-bold lg:text-4xl">Want This Done for You?</h2>
          <p className="mb-8 text-lg text-muted-foreground leading-relaxed">
            Reading is great. But if you'd rather have experts handle your marketing while you focus on your
            business, we can help.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/services"
              className="inline-flex items-center justify-center rounded-full bg-accent text-accent-foreground hover:bg-accent/90 px-8 py-3 font-medium transition-colors"
            >
              See Our Services
            </a>
            <a
              href="/#contact"
              className="inline-flex items-center justify-center rounded-full border-2 border-accent bg-transparent hover:bg-accent/10 px-8 py-3 font-medium transition-colors"
            >
              Book Free Consultation
            </a>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
