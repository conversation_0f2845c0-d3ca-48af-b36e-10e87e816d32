import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { getBlogPost, blogPosts } from "@/lib/blog-posts"
import { notFound } from "next/navigation"
import { Calendar, Clock, User, ArrowLeft } from "lucide-react"
import Link from "next/link"
import { BlogCard } from "@/components/blog-card"

export async function generateStaticParams() {
  return blogPosts.map((post) => ({
    slug: post.slug,
  }))
}

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const post = getBlogPost(params.slug)

  if (!post) {
    return {
      title: "Post Not Found",
    }
  }

  return {
    title: `${post.title} | Nexveria Blog`,
    description: post.description,
  }
}

export default function BlogPostPage({ params }: { params: { slug: string } }) {
  const post = getBlogPost(params.slug)

  if (!post) {
    notFound()
  }

  // Get related posts (same category, excluding current post)
  const relatedPosts = blogPosts
    .filter((p) => p.category === post.category && p.slug !== post.slug)
    .slice(0, 3)

  return (
    <main className="min-h-screen">
      <Navbar />

      <article className="container mx-auto px-6 pt-32 pb-20">
        <div className="max-w-4xl mx-auto">
          {/* Back Link */}
          <Link
            href="/blog"
            className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-accent mb-8 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Blog
          </Link>

          {/* Category Badge */}
          <div className="mb-4">
            <span className="inline-block rounded-full bg-accent/20 px-3 py-1 text-sm font-medium text-accent">
              {post.category}
            </span>
          </div>

          {/* Title */}
          <h1 className="mb-6 text-4xl font-bold leading-tight lg:text-5xl">{post.title}</h1>

          {/* Meta Info */}
          <div className="mb-8 flex flex-wrap items-center gap-6 text-sm text-muted-foreground border-b border-border pb-6">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span>{post.author}</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>
                {new Date(post.date).toLocaleDateString("en-US", {
                  month: "long",
                  day: "numeric",
                  year: "numeric",
                })}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>{post.readTime}</span>
            </div>
          </div>

          {/* Content */}
          <div className="prose prose-lg prose-neutral dark:prose-invert max-w-none">
            {post.content.split("\n\n").map((paragraph, index) => {
              // Handle headings
              if (paragraph.startsWith("## ")) {
                return (
                  <h2 key={index} className="text-2xl font-bold mt-12 mb-4">
                    {paragraph.replace("## ", "")}
                  </h2>
                )
              }

              // Handle bold text with **
              if (paragraph.startsWith("**") && paragraph.includes(":**")) {
                const parts = paragraph.split(":**")
                return (
                  <div key={index} className="mb-4">
                    <strong className="text-accent">{parts[0].replace("**", "")}</strong>
                    {parts[1]}
                  </div>
                )
              }

              // Handle lists
              if (paragraph.startsWith("- ")) {
                const items = paragraph.split("\n")
                return (
                  <ul key={index} className="list-disc pl-6 mb-6 space-y-2">
                    {items.map((item, i) => (
                      <li key={i}>{item.replace("- ", "")}</li>
                    ))}
                  </ul>
                )
              }

              // Handle checkboxes
              if (paragraph.includes("- [ ]")) {
                const items = paragraph.split("\n")
                return (
                  <ul key={index} className="space-y-2 mb-6">
                    {items.map((item, i) => {
                      if (item.includes("- [ ]")) {
                        return (
                          <li key={i} className="flex items-start gap-2">
                            <input
                              type="checkbox"
                              className="mt-1.5 h-4 w-4 rounded border-border"
                              disabled
                            />
                            <span>{item.replace("- [ ] ", "")}</span>
                          </li>
                        )
                      }
                      return null
                    })}
                  </ul>
                )
              }

              // Regular paragraphs
              if (paragraph.trim() && !paragraph.startsWith("#")) {
                return (
                  <p key={index} className="mb-6 leading-relaxed text-muted-foreground">
                    {paragraph}
                  </p>
                )
              }

              return null
            })}
          </div>

          {/* Author Bio */}
          <div className="mt-12 pt-8 border-t border-border">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 w-16 h-16 rounded-full bg-accent/20 flex items-center justify-center">
                <User className="h-8 w-8 text-accent" />
              </div>
              <div>
                <h3 className="font-semibold text-lg mb-1">Written by {post.author}</h3>
                <p className="text-muted-foreground text-sm">
                  Marketing specialist helping Kenyan businesses grow through practical, results-driven
                  strategies.
                </p>
              </div>
            </div>
          </div>
        </div>
      </article>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <section className="container mx-auto px-6 py-20 bg-muted/30">
          <div className="max-w-6xl mx-auto">
            <h2 className="mb-8 text-2xl font-bold">Related Articles</h2>
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {relatedPosts.map((relatedPost) => (
                <BlogCard key={relatedPost.slug} post={relatedPost} />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* CTA */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-4xl mx-auto rounded-2xl border border-accent/20 bg-gradient-to-br from-accent/5 to-transparent p-12 text-center">
          <h2 className="mb-4 text-3xl font-bold lg:text-4xl">Need Help Implementing This?</h2>
          <p className="mb-8 text-lg text-muted-foreground leading-relaxed">
            Reading is one thing. Execution is another. If you'd rather have experts handle your marketing,
            we're here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/services"
              className="inline-flex items-center justify-center rounded-full bg-accent text-accent-foreground hover:bg-accent/90 px-8 py-3 font-medium transition-colors"
            >
              See Our Services
            </Link>
            <Link
              href="/#contact"
              className="inline-flex items-center justify-center rounded-full border-2 border-accent bg-transparent hover:bg-accent/10 px-8 py-3 font-medium transition-colors"
            >
              Book Free Consultation
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
