import { Navbar } from "@/components/navbar"
import { NewHeroSection } from "@/components/new-hero-section"
import { ServicesOverview } from "@/components/services-overview"
import { TrustSection } from "@/components/trust-section"
import { WhyNexveria } from "@/components/why-nexveria"
import { ProjectsShowcase } from "@/components/projects-showcase"
import { ContactSection } from "@/components/contact-section"
import { Footer } from "@/components/footer"
// import { StatsSection } from "@/components/stats-section"

export default function Home() {
  return (
    <main className="min-h-screen">
      <Navbar />
      <NewHeroSection />
      {/* <StatsSection
        title="Trusted by Businesses Across Kenya"
        subtitle="From startups to established enterprises, we deliver results"
        stats={[
          { value: "30%", label: "Cost Reduction" },
          { value: "8+", label: "Years Experience" },
          { value: "30+", label: "Projects Delivered" },
          { value: "50+", label: "Happy Clients" },
        ]}
      /> */}
      <TrustSection />
      <ServicesOverview />
      <ProjectsShowcase />
      <WhyNexveria />
      <ContactSection />
      <Footer />
    </main>
  )
}
