import { CheckCircleIcon, UsersIcon, ClockIcon, ShieldIcon, LightbulbIcon, TrendingUpIcon } from "@/components/icons"

export function DevelopmentServices() {
  const services = [
    {
      icon: CheckCircleIcon,
      title: "Quality Assurance",
      description: "Rigorous testing processes ensure bug-free, reliable software that exceeds expectations.",
    },
    {
      icon: UsersIcon,
      title: "Dedicated Teams",
      description: "Work with experienced developers who become an extension of your team.",
    },
    {
      icon: ClockIcon,
      title: "Agile Methodology",
      description: "Iterative development with regular feedback loops keeps projects on track.",
    },
    {
      icon: ShieldIcon,
      title: "Security First",
      description: "Built-in security measures protect your data and comply with industry standards.",
    },
    {
      icon: LightbulbIcon,
      title: "Innovation Focus",
      description: "Leverage cutting-edge technologies to stay ahead of the competition.",
    },
    {
      icon: TrendingUpIcon,
      title: "Scalable Solutions",
      description: "Architecture designed to grow with your business without performance degradation.",
    },
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="mb-12 text-center">
        <h2 className="mb-4 text-4xl font-bold text-balance">What You Get From Our Custom Development Services</h2>
        <p className="text-lg text-muted-foreground">Comprehensive support at every stage</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {services.map((service, index) => {
          const Icon = service.icon
          return (
            <div key={index} className="rounded-lg border border-border bg-card p-6">
              <Icon className="mb-4 h-10 w-10 text-accent" />
              <h3 className="mb-3 text-xl font-semibold">{service.title}</h3>
              <p className="text-muted-foreground leading-relaxed">{service.description}</p>
            </div>
          )
        })}
      </div>
    </section>
  )
}
