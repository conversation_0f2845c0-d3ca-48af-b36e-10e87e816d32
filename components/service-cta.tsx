"use client"

import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

interface ServiceCTAProps {
  title: string
  description: string
  primaryCTA: string
  secondaryCTA?: string
  primaryHref?: string
  secondaryHref?: string
}

export function ServiceCTA({
  title,
  description,
  primaryCTA,
  secondaryCTA,
  primaryHref = "#",
  secondaryHref = "/",
}: ServiceCTAProps) {
  return (
    <section className="container mx-auto px-6 py-20">
      <div className="max-w-4xl mx-auto rounded-2xl border border-accent/20 bg-gradient-to-br from-accent/5 to-transparent p-12 text-center">
        <h2 className="mb-4 text-3xl font-bold lg:text-4xl">{title}</h2>
        <p className="mb-8 text-lg text-muted-foreground leading-relaxed">{description}</p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Button
            asChild
            size="lg"
            className="rounded-full bg-accent text-accent-foreground hover:bg-accent/90 px-8"
          >
            <Link href={primaryHref}>{primaryCTA}</Link>
          </Button>
          {secondaryCTA && (
            <Button
              asChild
              size="lg"
              variant="outline"
              className="rounded-full border-2 border-accent bg-transparent hover:bg-accent/10"
            >
              <Link href={secondaryHref}>{secondaryCTA}</Link>
            </Button>
          )}
        </div>
      </div>
    </section>
  )
}
