import { XIcon, CheckIcon } from "@/components/icons"

export function ComparisonSection() {
  const failures = [
    "Unclear requirements and scope creep",
    "Poor communication and misalignment",
    "Inadequate testing and quality issues",
    "Missed deadlines and budget overruns",
    "Technical debt and scalability problems",
  ]

  const solutions = [
    "Detailed discovery and documentation",
    "Daily standups and transparent reporting",
    "Comprehensive QA and automated testing",
    "Agile sprints with predictable delivery",
    "Modern architecture and best practices",
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="grid gap-8 lg:grid-cols-2">
        <div className="rounded-lg border border-destructive/30 bg-card p-8">
          <h3 className="mb-6 text-2xl font-bold">Common Failures in Custom Software Development Projects</h3>
          <ul className="space-y-4">
            {failures.map((failure, index) => (
              <li key={index} className="flex items-start gap-3">
                <div className="mt-1 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-destructive/20">
                  <XIcon className="h-3 w-3 text-destructive" />
                </div>
                <span className="text-muted-foreground">{failure}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="rounded-lg border border-accent/30 bg-card p-8">
          <h3 className="mb-6 text-2xl font-bold">{"Nexveria's"} Proven Solutions</h3>
          <ul className="space-y-4">
            {solutions.map((solution, index) => (
              <li key={index} className="flex items-start gap-3">
                <div className="mt-1 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-accent/20">
                  <CheckIcon className="h-3 w-3 text-accent" />
                </div>
                <span className="text-muted-foreground">{solution}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  )
}
