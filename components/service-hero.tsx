"use client"

import { HeroContactForm } from "@/components/hero-contact-form"

interface ServiceHeroProps {
  title: string
  subtitle: string
  description: string
  painPoint?: string
  showContactForm?: boolean
  formTitle?: string
}

export function ServiceHero({
  title,
  subtitle,
  description,
  painPoint,
  showContactForm = false,
  formTitle = "Start Your Project",
}: ServiceHeroProps) {
  if (showContactForm) {
    return (
      <section className="container mx-auto px-6 pt-32 pb-20">
        <div className="grid gap-12 lg:grid-cols-2 lg:gap-16">
          <div className="flex flex-col justify-center">
            <div className="mb-6 inline-block">
              <span className="text-sm font-medium text-accent">{subtitle}</span>
            </div>
            <h1 className="mb-6 text-5xl font-bold leading-tight text-balance lg:text-6xl">
              {title}
            </h1>
            <p className="mb-8 text-lg text-muted-foreground leading-relaxed text-pretty">
              {description}
            </p>
            {painPoint && (
              <p className="text-lg text-red-400/90 leading-relaxed text-pretty italic">
                {painPoint}
              </p>
            )}
          </div>

          <HeroContactForm title={formTitle} />
        </div>
      </section>
    )
  }

  return (
    <section className="container mx-auto px-6 pt-32 pb-20">
      <div className="max-w-4xl mx-auto text-center">
        <div className="mb-6 inline-block">
          <span className="text-sm font-medium text-accent">{subtitle}</span>
        </div>
        <h1 className="mb-6 text-5xl font-bold leading-tight text-balance lg:text-6xl">
          {title}
        </h1>
        <p className="mb-4 text-xl text-muted-foreground leading-relaxed text-pretty">
          {description}
        </p>
        {painPoint && (
          <p className="mb-8 text-lg text-red-400/90 leading-relaxed text-pretty italic">
            {painPoint}
          </p>
        )}
      </div>
    </section>
  )
}
