import { But<PERSON> } from "@/components/ui/button"

export function AIToolsSection() {
  const tools = [
    "<PERSON>urs<PERSON>",
    "Replit",
    "Bolt",
    "GitHub Copilot",
    "ChatGPT",
    "Claude",
    "Midjourney",
    "Stable Diffusion",
    "Vercel AI",
    "Hugging Face",
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="rounded-2xl border border-accent/30 bg-card p-12 text-center">
        <h2 className="mb-4 text-4xl font-bold text-balance">Launch Faster and Reduce Development Costs by 30%-40%</h2>
        <p className="mb-8 text-lg text-muted-foreground">
          We leverage cutting-edge AI tools to accelerate development and deliver exceptional results
        </p>
        <Button className="mb-12 rounded-full bg-accent px-8 text-accent-foreground hover:bg-accent/90">
          Book Your Consultation
        </Button>

        <div className="grid grid-cols-2 gap-4 md:grid-cols-5">
          {tools.map((tool, index) => (
            <div
              key={index}
              className="rounded-lg border border-border bg-background p-4 text-center transition-colors hover:border-accent/50"
            >
              <span className="text-sm font-medium">{tool}</span>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
