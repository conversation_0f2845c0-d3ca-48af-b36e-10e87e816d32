"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

const heroFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  company: z.string().min(1, "Company name is required"),
  message: z.string().min(10, "Message must be at least 10 characters"),
})

type HeroFormData = z.infer<typeof heroFormSchema>

interface HeroContactFormProps {
  title?: string
}

export function HeroContactForm({ title = "Start Your Project" }: HeroContactFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<HeroFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(heroFormSchema),
  })

  const onSubmit = async (data: HeroFormData) => {
    setIsSubmitting(true)

    try {
      const [firstName, ...lastNameParts] = data.name.split(" ")
      const lastName = lastNameParts.join(" ") || firstName

      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          firstName,
          lastName,
          email: data.email,
          company: data.company,
          phone: "N/A",
          message: data.message,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to send message")
      }

      const responseData = await response.json()
      console.log("✅ Success response:", responseData)

      toast.success("Message sent successfully!", {
        description: "We'll get back to you as soon as possible.",
      })

      reset()
    } catch (error) {
      toast.error("Failed to send message", {
        description: error instanceof Error ? error.message : "Please try again later.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="rounded-lg border border-border bg-card p-8">
      <h3 className="mb-6 text-2xl font-semibold">{title}</h3>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <Input
            {...register("name")}
            placeholder="Your Name"
            className="bg-background"
            disabled={isSubmitting}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-500">{errors.name.message}</p>
          )}
        </div>
        <div>
          <Input
            {...register("email")}
            type="email"
            placeholder="Email Address"
            className="bg-background"
            disabled={isSubmitting}
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
          )}
        </div>
        <div>
          <Input
            {...register("company")}
            placeholder="Company"
            className="bg-background"
            disabled={isSubmitting}
          />
          {errors.company && (
            <p className="mt-1 text-sm text-red-500">{errors.company.message}</p>
          )}
        </div>
        <div>
          <Textarea
            {...register("message")}
            placeholder="Tell us about your project"
            className="min-h-[120px] bg-background"
            disabled={isSubmitting}
          />
          {errors.message && (
            <p className="mt-1 text-sm text-red-500">{errors.message.message}</p>
          )}
        </div>
        <Button
          type="submit"
          className="w-full rounded-full bg-accent text-accent-foreground hover:bg-accent/90"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Sending..." : "Submit Request"}
        </Button>
      </form>
    </div>
  )
}
