"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { 
  BriefcaseIcon, 
  PaletteIcon, 
  SearchIcon, 
  UsersIcon, 
  VideoIcon,
  BuildingIcon
} from "lucide-react"

const personas = [
  {
    icon: BuildingIcon,
    title: "Small Business Owners",
    problem: "Wasting hours replying to messages & emails",
    solution: "Let AI handle your inbox while you grow your business",
    automations: ["Customer responses", "Email management", "Social media posting", "Lead follow-up"],
    timesSaved: "15+ hours/week",
    color: "bg-blue-500/10 text-blue-600 dark:text-blue-400"
  },
  {
    icon: PaletteIcon,
    title: "Freelancers & Creatives",
    problem: "Posting & following up drains creative time",
    solution: "Spend more time creating, less time scheduling",
    automations: ["Social media scheduling", "Client follow-ups", "Portfolio updates", "Invoice reminders"],
    timesSaved: "12+ hours/week",
    color: "bg-purple-500/10 text-purple-600 dark:text-purple-400"
  },
  {
    icon: SearchIcon,
    title: "Job Seekers",
    problem: "Tired of repetitive job applications",
    solution: "Your AI assistant applies to jobs while you focus on interviews",
    automations: ["Job applications", "Cover letter writing", "Follow-up emails", "Interview scheduling"],
    timesSaved: "20+ hours/week",
    color: "bg-green-500/10 text-green-600 dark:text-green-400"
  },
  {
    icon: UsersIcon,
    title: "Office Admins",
    problem: "Routine data entry & coordination",
    solution: "Turn routine tasks into one-click automations",
    automations: ["Data entry", "Report generation", "Meeting scheduling", "Document processing"],
    timesSaved: "18+ hours/week",
    color: "bg-orange-500/10 text-orange-600 dark:text-orange-400"
  },
  {
    icon: VideoIcon,
    title: "Content Creators",
    problem: "Posting manually across platforms",
    solution: "Post everywhere in seconds. Stay consistent effortlessly",
    automations: ["Multi-platform posting", "Content repurposing", "Engagement responses", "Analytics tracking"],
    timesSaved: "10+ hours/week",
    color: "bg-pink-500/10 text-pink-600 dark:text-pink-400"
  },
  {
    icon: BriefcaseIcon,
    title: "Professionals",
    problem: "Administrative tasks eating into productive time",
    solution: "Focus on high-value work while AI handles the rest",
    automations: ["Email sorting", "Calendar management", "Expense tracking", "Client communications"],
    timesSaved: "8+ hours/week",
    color: "bg-indigo-500/10 text-indigo-600 dark:text-indigo-400"
  }
]

export function TaskAutomationPersonas() {
  return (
    <section className="container mx-auto px-6 py-20">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="mb-4 text-3xl font-bold lg:text-4xl">
            Who Benefits from Task Automation?
          </h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Whether you're a business owner, creative professional, or busy individual, 
            our AI assistant adapts to your specific needs and workflows.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {personas.map((persona, index) => {
            const IconComponent = persona.icon
            return (
              <Card key={index} className="hover:border-accent/50 transition-colors group">
                <CardHeader>
                  <div className={`w-12 h-12 rounded-lg ${persona.color} flex items-center justify-center mb-3`}>
                    <IconComponent className="h-6 w-6" />
                  </div>
                  <CardTitle className="text-xl">{persona.title}</CardTitle>
                  <CardDescription className="text-red-400/90 italic">
                    "{persona.problem}"
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-3 bg-accent/10 rounded-lg border border-accent/20">
                    <p className="text-sm font-medium text-accent mb-2">Our Solution:</p>
                    <p className="text-sm">{persona.solution}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium mb-2">What We Automate:</p>
                    <div className="flex flex-wrap gap-1">
                      {persona.automations.map((automation, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {automation}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2">
                    <div>
                      <p className="text-sm text-muted-foreground">Time Saved:</p>
                      <p className="font-bold text-accent">{persona.timesSaved}</p>
                    </div>
                    <Button 
                      asChild 
                      size="sm" 
                      className="rounded-full bg-accent/10 text-accent hover:bg-accent hover:text-accent-foreground border border-accent/20"
                    >
                      <Link href="/#contact">Get Started</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        <div className="text-center mt-12">
          <div className="inline-block p-6 bg-card border border-border rounded-lg">
            <h3 className="text-xl font-semibold mb-2">Don't See Your Use Case?</h3>
            <p className="text-muted-foreground mb-4">
              We customize automation solutions for any repetitive workflow. 
              Let's discuss your specific needs.
            </p>
            <Button asChild className="rounded-full bg-accent text-accent-foreground hover:bg-accent/90">
              <Link href="/#contact">Book Custom Consultation</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
