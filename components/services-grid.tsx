import {
  CodeIcon,
  CloudIcon,
  SmartphoneIcon,
  DatabaseIcon,
  ShieldIcon,
  ZapIcon,
  CpuIcon,
  GlobeIcon,
  LayersIcon,
} from "@/components/icons"

export function ServicesGrid() {
  const services = [
    {
      icon: CodeIcon,
      title: "Custom Software Development",
      description:
        "Build tailored applications that perfectly fit your business requirements and scale with your growth.",
    },
    {
      icon: CloudIcon,
      title: "Cloud Engineering",
      description: "Migrate and optimize your infrastructure with modern cloud-native architectures.",
    },
    {
      icon: SmartphoneIcon,
      title: "Mobile App Development",
      description: "Create engaging iOS and Android applications with seamless user experiences.",
    },
    {
      icon: DatabaseIcon,
      title: "Data Engineering",
      description: "Transform raw data into actionable insights with robust data pipelines and analytics.",
    },
    {
      icon: ShieldIcon,
      title: "DevOps & Security",
      description: "Implement CI/CD pipelines and security best practices for reliable deployments.",
    },
    {
      icon: ZapIcon,
      title: "API Development",
      description: "Design and build scalable APIs that power your digital ecosystem.",
    },
    {
      icon: CpuIcon,
      title: "AI & Machine Learning",
      description: "Leverage artificial intelligence to automate processes and gain competitive advantages.",
    },
    {
      icon: GlobeIcon,
      title: "Web Applications",
      description: "Develop modern, responsive web applications with cutting-edge technologies.",
    },
    {
      icon: LayersIcon,
      title: "System Integration",
      description: "Connect disparate systems and streamline workflows across your organization.",
    },
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="mb-12 text-center">
        <h2 className="mb-4 text-4xl font-bold text-balance">Custom Software Development Offerings</h2>
        <p className="text-lg text-muted-foreground">Comprehensive solutions for every stage of your digital journey</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {services.map((service, index) => {
          const Icon = service.icon
          return (
            <div
              key={index}
              className="rounded-lg border border-border bg-card p-6 transition-colors hover:border-accent/50"
            >
              <Icon className="mb-4 h-10 w-10 text-accent" />
              <h3 className="mb-3 text-xl font-semibold">{service.title}</h3>
              <p className="text-muted-foreground leading-relaxed">{service.description}</p>
            </div>
          )
        })}
      </div>
    </section>
  )
}
