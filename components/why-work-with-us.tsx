import { AwardIcon, TargetIcon, ZapIcon, ShieldIcon, UsersIcon } from "@/components/icons"

export function WhyWorkWithUs() {
  const reasons = [
    {
      icon: AwardIcon,
      title: "Proven Track Record",
      description: "500+ successful projects delivered across diverse industries",
    },
    {
      icon: TargetIcon,
      title: "Business-First Approach",
      description: "We focus on outcomes that drive your business forward",
    },
    {
      icon: ZapIcon,
      title: "Rapid Development",
      description: "Leverage AI tools to accelerate delivery by 30-40%",
    },
    {
      icon: ShieldIcon,
      title: "Enterprise Security",
      description: "Bank-level security and compliance built into every solution",
    },
    {
      icon: UsersIcon,
      title: "Dedicated Support",
      description: "Ongoing maintenance and optimization after launch",
    },
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="mb-12 text-center">
        <h2 className="mb-4 text-4xl font-bold">Why Work with Nexveria?</h2>
        <p className="text-lg text-muted-foreground">The partner you can trust for mission-critical software</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {reasons.slice(0, 3).map((reason, index) => {
          const Icon = reason.icon
          return (
            <div key={index} className="rounded-lg border border-border bg-card p-8">
              <Icon className="mb-4 h-10 w-10 text-accent" />
              <h3 className="mb-3 text-xl font-semibold">{reason.title}</h3>
              <p className="text-muted-foreground leading-relaxed">{reason.description}</p>
            </div>
          )
        })}
      </div>
      <div className="mt-6 grid gap-6 md:grid-cols-2 lg:grid-cols-2">
        {reasons.slice(3).map((reason, index) => {
          const Icon = reason.icon
          return (
            <div key={index} className="rounded-lg border border-border bg-card p-8">
              <Icon className="mb-4 h-10 w-10 text-accent" />
              <h3 className="mb-3 text-xl font-semibold">{reason.title}</h3>
              <p className="text-muted-foreground leading-relaxed">{reason.description}</p>
            </div>
          )
        })}
      </div>
    </section>
  )
}
