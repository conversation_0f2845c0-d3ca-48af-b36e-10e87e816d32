export function IndustryExpertise() {
  const industries = [
    {
      title: "Healthcare",
      points: ["HIPAA Compliance", "Telemedicine Platforms", "EHR Integration", "Patient Portals"],
    },
    {
      title: "Financial Services",
      points: ["Payment Processing", "Fraud Detection", "Regulatory Compliance", "Trading Platforms"],
    },
    {
      title: "E-Commerce",
      points: ["Shopping Cart Systems", "Inventory Management", "Payment Gateways", "Analytics"],
    },
    {
      title: "Manufacturing",
      points: ["IoT Integration", "Supply Chain", "Quality Control", "Predictive Maintenance"],
    },
    {
      title: "Education",
      points: ["Learning Management", "Virtual Classrooms", "Student Portals", "Assessment Tools"],
    },
    {
      title: "Real Estate",
      points: ["Property Management", "CRM Systems", "Virtual Tours", "Market Analytics"],
    },
    {
      title: "Logistics",
      points: ["Fleet Management", "Route Optimization", "Warehouse Systems", "Tracking Solutions"],
    },
    {
      title: "Entertainment",
      points: ["Streaming Platforms", "Content Management", "User Engagement", "Analytics"],
    },
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="mb-12 text-center">
        <h2 className="mb-4 text-4xl font-bold">Core Industry Expertise</h2>
        <p className="text-lg text-muted-foreground">Deep domain knowledge across sectors</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {industries.map((industry, index) => (
          <div key={index} className="rounded-lg border border-border bg-card p-6">
            <h3 className="mb-4 text-xl font-semibold">{industry.title}</h3>
            <ul className="space-y-2">
              {industry.points.map((point, idx) => (
                <li key={idx} className="flex items-start gap-2 text-sm text-muted-foreground">
                  <span className="mt-1 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-accent" />
                  {point}
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </section>
  )
}
