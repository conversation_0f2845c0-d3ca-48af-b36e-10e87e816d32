"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { MessageCircleIcon, XIcon } from "lucide-react"

export function WhatsAppChatWidget() {
  const [isVisible, setIsVisible] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)

  // Show widget after a delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, 3000) // Show after 3 seconds

    return () => clearTimeout(timer)
  }, [])

  const whatsappNumber = "+254732369907"
  const defaultMessage = "Hi! I'm interested in learning more about your Task Automation Service. Can you help me get started?"

  const handleWhatsAppClick = () => {
    const encodedMessage = encodeURIComponent(defaultMessage)
    const whatsappUrl = `https://wa.me/${whatsappNumber.replace('+', '')}?text=${encodedMessage}`
    window.open(whatsappUrl, '_blank')
  }

  if (!isVisible) return null

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Expanded chat preview */}
      {isExpanded && (
        <div className="mb-4 w-80 bg-card border border-border rounded-lg shadow-lg animate-in slide-in-from-bottom-2">
          <div className="p-4 border-b border-border bg-green-500 text-white rounded-t-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <MessageCircleIcon className="h-5 w-5" />
                </div>
                <div>
                  <h4 className="font-semibold">Nexveria Support</h4>
                  <p className="text-xs opacity-90">Typically replies instantly</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(false)}
                className="text-white hover:bg-white/20 h-8 w-8 p-0"
              >
                <XIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="p-4 space-y-3">
            <div className="bg-muted p-3 rounded-lg rounded-bl-none max-w-[85%]">
              <p className="text-sm">
                👋 Hi there! Ready to automate your repetitive tasks and save 20+ hours per week?
              </p>
            </div>
            
            <div className="bg-muted p-3 rounded-lg rounded-bl-none max-w-[85%]">
              <p className="text-sm">
                I can help you get started with our Task Automation Service. What would you like to automate first?
              </p>
            </div>

            <div className="space-y-2">
              <button 
                onClick={handleWhatsAppClick}
                className="w-full text-left p-2 text-sm bg-accent/10 hover:bg-accent/20 rounded border border-accent/20 transition-colors"
              >
                📱 Social Media Automation
              </button>
              <button 
                onClick={handleWhatsAppClick}
                className="w-full text-left p-2 text-sm bg-accent/10 hover:bg-accent/20 rounded border border-accent/20 transition-colors"
              >
                📧 Email & Customer Responses
              </button>
              <button 
                onClick={handleWhatsAppClick}
                className="w-full text-left p-2 text-sm bg-accent/10 hover:bg-accent/20 rounded border border-accent/20 transition-colors"
              >
                💼 Job Application Automation
              </button>
              <button 
                onClick={handleWhatsAppClick}
                className="w-full text-left p-2 text-sm bg-accent/10 hover:bg-accent/20 rounded border border-accent/20 transition-colors"
              >
                📊 Data Entry & Reports
              </button>
            </div>
          </div>

          <div className="p-4 border-t border-border">
            <Button 
              onClick={handleWhatsAppClick}
              className="w-full bg-green-500 hover:bg-green-600 text-white rounded-full"
            >
              <MessageCircleIcon className="h-4 w-4 mr-2" />
              Start WhatsApp Chat
            </Button>
          </div>
        </div>
      )}

      {/* Chat button */}
      <div className="relative">
        <Button
          onClick={() => setIsExpanded(!isExpanded)}
          className="h-14 w-14 rounded-full bg-green-500 hover:bg-green-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 animate-pulse"
        >
          {isExpanded ? (
            <XIcon className="h-6 w-6" />
          ) : (
            <MessageCircleIcon className="h-6 w-6" />
          )}
        </Button>
        
        {/* Notification dot */}
        {!isExpanded && (
          <div className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-bold">1</span>
          </div>
        )}
      </div>

      {/* Tooltip */}
      {!isExpanded && (
        <div className="absolute bottom-16 right-0 bg-card border border-border rounded-lg p-2 shadow-lg whitespace-nowrap animate-in slide-in-from-bottom-1">
          <p className="text-sm font-medium">💬 Need help with automation?</p>
          <p className="text-xs text-muted-foreground">Click to chat on WhatsApp</p>
        </div>
      )}
    </div>
  )
}
