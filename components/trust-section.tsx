export function TrustSection() {
  const stats = [
    {
      number: "30+",
      label: "Projects Delivered",
      description: "From MVPs to enterprise solutions",
    },
    {
      number: "50+",
      label: "Active Clients",
      description: "Businesses we help grow every month",
    },
    {
      number: "8+ Years",
      label: "Industry Experience",
      description: "Building solutions that scale",
    },
    {
      number: "98%",
      label: "Client Satisfaction",
      description: "Projects delivered on time and budget",
    },
  ]

  return (
    <section className="container mx-auto px-6 py-20 bg-muted/30">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="mb-4 text-3xl font-bold lg:text-4xl">Trusted by Businesses Across Kenya</h2>
          <p className="text-lg text-muted-foreground">
            From startups to established enterprises, we deliver results
          </p>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl font-bold text-accent mb-2">{stat.number}</div>
              <div className="text-lg font-semibold mb-1">{stat.label}</div>
              <p className="text-sm text-muted-foreground">{stat.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
