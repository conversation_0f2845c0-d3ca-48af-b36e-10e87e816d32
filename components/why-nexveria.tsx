export function WhyNexveria() {
  const reasons = [
    {
      title: "Technical Excellence Meets Business Results",
      description:
        "We don't just write code or run ads. We understand your business goals and build solutions that move the needle on revenue, efficiency, and growth.",
    },
    {
      title: "Kenya-First Approach",
      description:
        "We understand the Kenyan market, M-Pesa integration, mobile-first users, and the unique challenges of doing business here. No copy-paste solutions from abroad.",
    },
    {
      title: "Full-Stack Capability",
      description:
        "Need software built? We do that. Need customers? We do that too. One partner for your entire technology and growth journey.",
    },
    {
      title: "Transparent & Reliable",
      description:
        "No hidden fees. No surprise charges. No excuses. We deliver what we promise, on time and on budget. That's how we've built our reputation.",
    },
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="mb-4 text-3xl font-bold lg:text-4xl">Why Choose Nexveria?</h2>
          <p className="text-lg text-muted-foreground">
            We're different from typical agencies and dev shops
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {reasons.map((reason, index) => (
            <div
              key={index}
              className="rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
            >
              <h3 className="text-xl font-semibold mb-3">{reason.title}</h3>
              <p className="text-muted-foreground leading-relaxed">{reason.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
