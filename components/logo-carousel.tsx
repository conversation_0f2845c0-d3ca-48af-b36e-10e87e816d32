export function LogoCarousel() {
  const logos = [
    "AWS",
    "Google Cloud",
    "Microsoft Azure",
    "Docker",
    "Kubernetes",
    "React",
    "Next.js",
    "Node.js",
    "Python",
    "PostgreSQL",
  ]

  return (
    <section className="border-y border-border bg-card/50 py-8">
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-center gap-12 overflow-hidden">
          {logos.map((logo, index) => (
            <div key={index} className="flex-shrink-0">
              <span className="text-sm font-medium text-muted-foreground">{logo}</span>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
