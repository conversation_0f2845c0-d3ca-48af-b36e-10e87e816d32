"use client"

import {
  Accordion,
  Accordion<PERSON>ontent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion"

interface FAQ {
  question: string
  answer: string
}

interface ServiceFAQProps {
  title?: string
  faqs: FAQ[]
}

export function ServiceFAQ({ title = "Frequently Asked Questions", faqs }: ServiceFAQProps) {
  return (
    <section className="container mx-auto px-6 py-20">
      <div className="max-w-3xl mx-auto">
        <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">{title}</h2>
        <Accordion type="single" collapsible className="w-full">
          {faqs.map((faq, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
              <AccordionContent className="text-muted-foreground leading-relaxed">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  )
}
