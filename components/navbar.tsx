"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu"
import { Code2Icon } from "@/components/icons"
import { MoonIcon, SunIcon } from "lucide-react"
import { useTheme } from "next-themes"

const menuItems = {
  engineering: {
    title: "Engineering",
    items: [{
      title: "Custom Software Development",
      description: "Bespoke applications tailored to your business",
      href: "/services/custom-software"
    }, 
    {
      title: "Cloud Engineering",
      description: "AWS, Azure & Google Cloud infrastructure",
      href: "/services/cloud-engineering"
    },
    {
      title: "Application Modernization",
      description: "Upgrade legacy systems to modern tech",
      href: "/services/application-modernization"
    },
    {
      title: "DevOps & Automation",
      description: "CI/CD pipelines and infrastructure automation",
      href: "/services/devops"
    }]
  },
  data: {
    title: ""
  }
}

export function Navbar() {
  const { theme, setTheme } = useTheme()

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 border-b border-border/40 bg-background/80 backdrop-blur-lg">
      <div className="container mx-auto flex items-center justify-between px-6 py-4">
        <Link href="/" className="flex items-center gap-2">
          <Code2Icon className="h-8 w-8 text-accent" />
          <span className="text-xl font-bold">Nexveria</span>
        </Link>

        <NavigationMenu viewport={false} className="lg:flex">
          <NavigationMenuList className="gap-2">
            <NavigationMenuItem>
              <NavigationMenuTrigger className="bg-transparent">{menuItems.engineering.title}</NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid w-[450px] gap-3 p-4">
                  {menuItems.engineering.items.map((item) => (
                    <li key={item.title}>
                      <NavigationMenuLink asChild>
                        <Link href={item.href}
                          className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/10 text-popover-foreground"
                        >
                          <div className="text-sm font-medium leading-none text-foreground">{item.title}</div>
                          <p className="line-clamp-2 text-sm leading-snug">{item.description}</p>
                        </Link>
                      </NavigationMenuLink>
                    </li>
                  ))}
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <NavigationMenuTrigger className="bg-transparent">Data & AI</NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid w-[450px] gap-3 p-4">
                  <li>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/services/ai-development"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/10 text-popover-foreground"
                      >
                        <div className="text-sm font-medium leading-none text-foreground">AI Development</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Machine learning & AI-powered solutions
                        </p>
                      </Link>
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/services/ai-chatbot"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/10 text-popover-foreground"
                      >
                        <div className="text-sm font-medium leading-none text-foreground">AI Chatbot Development</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Intelligent chatbots for customer service
                        </p>
                      </Link>
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/services/data-engineering"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/10 text-popover-foreground"
                      >
                        <div className="text-sm font-medium leading-none text-foreground">Data Engineering</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Data pipelines, warehousing & analytics
                        </p>
                      </Link>
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/services/business-intelligence"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/10 text-popover-foreground"
                      >
                        <div className="text-sm font-medium leading-none text-foreground">Business Intelligence</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Dashboards, reporting & data visualization
                        </p>
                      </Link>
                    </NavigationMenuLink>
                  </li>
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <NavigationMenuTrigger className="bg-transparent">Marketing & Growth</NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid w-[300px] gap-4">
                  <li>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/services/task-automation"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/10 text-popover-foreground"
                      >
                        <div className="text-sm font-medium leading-none text-foreground">Task Automation Service</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          AI assistant for social media, emails & repetitive tasks
                        </p>
                      </Link>
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/services/whatsapp-automation"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/10 text-popover-foreground"
                      >
                        <div className="text-sm font-medium leading-none text-foreground">WhatsApp Automation</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          24/7 automated responses and lead capture
                        </p>
                      </Link>
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/services/social-media-management"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/10 text-popover-foreground"
                      >
                        <div className="text-sm font-medium leading-none text-foreground">Social Media Management</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Done-for-you content that drives customers
                        </p>
                      </Link>
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/services/google-ads"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/10 text-popover-foreground"
                      >
                        <div className="text-sm font-medium leading-none text-foreground">Google Ads</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Show up when customers are searching
                        </p>
                      </Link>
                    </NavigationMenuLink>
                  </li>
                  <li>
                    <NavigationMenuLink asChild>
                      <Link
                        href="/services/facebook-ads"
                        className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/10 text-popover-foreground"
                      >
                        <div className="text-sm font-medium leading-none text-foreground">Facebook & Instagram Ads</div>
                        <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                          Targeted ads that stop the scroll
                        </p>
                      </Link>
                    </NavigationMenuLink>
                  </li>
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <NavigationMenuLink asChild className={navigationMenuTriggerStyle()}>
                <Link href="/blog">Blog</Link>
              </NavigationMenuLink>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <NavigationMenuLink asChild className={navigationMenuTriggerStyle()}>
                <Link href="#">About Us</Link>
              </NavigationMenuLink>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>

        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            className="rounded-full"
          >
            <SunIcon className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <MoonIcon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Toggle theme</span>
          </Button>
          <Button asChild className="rounded-full border-2 border-accent bg-transparent px-6 shadow-[0_0_15px_rgba(112,224,200,0.3)] hover:bg-accent/10 text-foreground">
            <Link href="/#contact">Get in Touch</Link>
          </Button>
        </div>
      </div>
    </nav>
  )
}
