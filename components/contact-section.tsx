"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { MailIcon, PhoneIcon, MapPinIcon } from "@/components/icons"

const contactFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  company: z.string().min(1, "Company name is required"),
  phone: z.string().min(1, "Phone number is required"),
  message: z.string().min(10, "Message must be at least 10 characters"),
})

type ContactFormData = z.infer<typeof contactFormSchema>

export function ContactSection() {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
  })

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true)

    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to send message")
      }

      const responseData = await response.json()

      toast.success("Message sent successfully!", {
        description: "We'll get back to you as soon as possible.",
      })

      reset()
    } catch (error) {
      toast.error("Failed to send message", {
        description: error instanceof Error ? error.message : "Please try again later.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <section id="contact" className="container mx-auto px-6 py-20">
      <div className="grid gap-12 lg:grid-cols-2">
        <div>
          <h2 className="mb-6 text-4xl font-bold text-balance">Share Your Business Goals with Technical Experts</h2>
          <p className="mb-8 text-lg text-muted-foreground leading-relaxed">
            {"Let's"} discuss how we can help you achieve your software development objectives. Our team is ready to
            provide expert guidance tailored to your needs.
          </p>

          <div className="mb-8 space-y-4">
            <div className="flex items-center gap-3">
              <MailIcon className="h-5 w-5 text-accent" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center gap-3">
              <PhoneIcon className="h-5 w-5 text-accent" />
              <span>+254 (732) 369-907</span>
            </div>
            <div className="flex items-center gap-3">
              <MapPinIcon className="h-5 w-5 text-accent" />
              <span>Kisumu, Kenya</span>
            </div>
          </div>

          <div className="rounded-lg border border-border bg-card p-6">
            <p className="mb-4 italic text-muted-foreground">
              &ldquo;Working with Nexveria was a game-changer for our business. They delivered a robust platform that
              exceeded our expectations.&rdquo;
            </p>
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-full bg-accent/20" />
              <div>
                <div className="font-semibold">Jennifer Ogallo</div>
                <div className="text-sm text-muted-foreground">Business Owner</div>
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-lg border border-border bg-card p-8">
          <h3 className="mb-6 text-2xl font-semibold">Get Started Today</h3>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Input
                  {...register("firstName")}
                  placeholder="First Name"
                  className="bg-background"
                  disabled={isSubmitting}
                />
                {errors.firstName && (
                  <p className="mt-1 text-sm text-red-500">{errors.firstName.message}</p>
                )}
              </div>
              <div>
                <Input
                  {...register("lastName")}
                  placeholder="Last Name"
                  className="bg-background"
                  disabled={isSubmitting}
                />
                {errors.lastName && (
                  <p className="mt-1 text-sm text-red-500">{errors.lastName.message}</p>
                )}
              </div>
            </div>

            <div>
              <Input
                {...register("email")}
                type="email"
                placeholder="Work Email"
                className="bg-background"
                disabled={isSubmitting}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>

            <div>
              <Input
                {...register("company")}
                placeholder="Company Name"
                className="bg-background"
                disabled={isSubmitting}
              />
              {errors.company && (
                <p className="mt-1 text-sm text-red-500">{errors.company.message}</p>
              )}
            </div>

            <div>
              <Input
                {...register("phone")}
                placeholder="Phone Number"
                className="bg-background"
                disabled={isSubmitting}
              />
              {errors.phone && (
                <p className="mt-1 text-sm text-red-500">{errors.phone.message}</p>
              )}
            </div>

            <div>
              <Textarea
                {...register("message")}
                placeholder="Tell us about your project requirements"
                className="min-h-[120px] bg-background"
                disabled={isSubmitting}
              />
              {errors.message && (
                <p className="mt-1 text-sm text-red-500">{errors.message.message}</p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full rounded-full bg-accent text-accent-foreground hover:bg-accent/90"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Sending..." : "Send Message"}
            </Button>
          </form>
        </div>
      </div>
    </section>
  )
}
