import Link from "next/link"
import { Code, Cloud, Brain, Cog, MessageSquare, Share2, Target, TrendingUp } from "lucide-react"

export function ServicesOverview() {
  const softwareServices = [
    {
      icon: Code,
      title: "Custom Software Development",
      description: "Bespoke applications tailored to your exact business needs",
      tags: ["Web Apps", "Mobile Apps", "AI Solutions", "Cloud Infrastructure"],
      href: "/services/custom-software",
      gradient: "from-blue-500/20 to-cyan-500/20",
    },
    {
      icon: Cloud,
      title: "Cloud Engineering",
      description: "AWS, Azure, and Google Cloud infrastructure",
      tags: ["Migration", "Optimization", "Cost Savings", "24/7 Uptime"],
      href: "/services/cloud-engineering",
      gradient: "from-purple-500/20 to-pink-500/20",
    },
    {
      icon: Brain,
      title: "AI Development",
      description: "Machine learning solutions that automate decisions",
      tags: ["Predictive Analytics", "Computer Vision", "NLP", "95% Accuracy"],
      href: "/services/ai-development",
      gradient: "from-violet-500/20 to-purple-500/20",
    },
    {
      icon: Cog,
      title: "DevOps & Automation",
      description: "CI/CD pipelines and deployment automation",
      tags: ["Deploy 10x Faster", "Auto-Testing", "Infrastructure as Code", "Monitoring"],
      href: "/services/devops",
      gradient: "from-green-500/20 to-emerald-500/20",
    },
  ]

  const marketingServices = [
    {
      icon: MessageSquare,
      title: "WhatsApp Automation",
      description: "24/7 automated responses and lead capture",
      tags: ["Never Miss Leads", "24/7 Active", "Auto Replies", "From KES 15K/mo"],
      href: "/services/whatsapp-automation",
      gradient: "from-green-500/20 to-lime-500/20",
    },
    {
      icon: Share2,
      title: "Social Media Management",
      description: "Done-for-you content that drives customers",
      tags: ["Daily Posting", "Custom Graphics", "Full Engagement", "From KES 25K/mo"],
      href: "/services/social-media-management",
      gradient: "from-pink-500/20 to-rose-500/20",
    },
    {
      icon: Target,
      title: "Google Ads",
      description: "Show up when customers search for what you sell",
      tags: ["Top of Google", "Pay Per Click", "Qualified Leads", "From KES 20K/mo"],
      href: "/services/google-ads",
      gradient: "from-yellow-500/20 to-orange-500/20",
    },
    {
      icon: TrendingUp,
      title: "Facebook & Instagram Ads",
      description: "Targeted campaigns that stop the scroll",
      tags: ["Laser Targeting", "Stop the Scroll", "Retargeting", "From KES 18K/mo"],
      href: "/services/facebook-ads",
      gradient: "from-blue-500/20 to-indigo-500/20",
    },
  ]

  return (
    <section id="services" className="container mx-auto px-6 py-20">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="mb-4 text-3xl font-bold lg:text-4xl">Complete Technology & Growth Solutions</h2>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Whether you need custom software built or customers driven to your business, we've got you covered.
          </p>
        </div>

        {/* Software Development Services */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold mb-8">Software Development</h3>
          <div className="grid md:grid-cols-2 gap-8">
            {softwareServices.map((service, index) => {
              const Icon = service.icon
              return (
                <Link
                  key={index}
                  href={service.href}
                  className="group rounded-2xl border border-border hover:border-accent/50 transition-all duration-300 hover:shadow-[0_0_30px_rgba(112,224,200,0.15)]"
                >
                  {/* Content */}
                  <div className="p-8">
                    <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-xl bg-accent/20 backdrop-blur-sm">
                      <Icon className="h-8 w-8 text-accent" />
                    </div>

                    <h3 className="mb-3 text-2xl font-bold group-hover:text-accent transition-colors">
                      {service.title}
                    </h3>

                    <p className="mb-6 text-muted-foreground leading-relaxed">
                      {service.description}
                    </p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2">
                      {service.tags.map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="inline-flex items-center rounded-full bg-accent/10 backdrop-blur-sm border border-accent/20 px-3 py-1 text-xs font-medium text-accent"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Hover Arrow */}
                  <div className="absolute bottom-8 right-8 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-accent text-accent-foreground">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        </div>

        {/* Marketing Services */}
        <div>
          <h3 className="text-2xl font-bold mb-8">Marketing & Growth</h3>
          <div className="grid md:grid-cols-2 gap-8">
            {marketingServices.map((service, index) => {
              const Icon = service.icon
              return (
                <Link
                  key={index}
                  href={service.href}
                  className="group rounded-2xl border border-border hover:border-accent/50 transition-all duration-300 hover:shadow-[0_0_30px_rgba(112,224,200,0.15)]"
                >
                  {/* Content */}
                  <div className="p-8">
                    <div className="mb-6 inline-flex h-16 w-16 items-center justify-center rounded-xl bg-accent/20 backdrop-blur-sm">
                      <Icon className="h-8 w-8 text-accent" />
                    </div>

                    <h3 className="mb-3 text-2xl font-bold group-hover:text-accent transition-colors">
                      {service.title}
                    </h3>

                    <p className="mb-6 text-muted-foreground leading-relaxed">
                      {service.description}
                    </p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2">
                      {service.tags.map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="inline-flex items-center rounded-full bg-accent/10 backdrop-blur-sm border border-accent/20 px-3 py-1 text-xs font-medium text-accent"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Hover Arrow */}
                  <div className="absolute bottom-8 right-8 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-accent text-accent-foreground">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}
