export function CustomersSection() {
  const customers = [
    {
      name: "<PERSON>",
      title: "Business Owner",
      image: "/pexels-anthonyshkraba-production-8837630.jpg",
      quote: "Nexveria transformed our vision into reality",
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      title: "School Admin",
      image: "/pexels-diva-plavalaguna-6149781.jpg",
      quote: "Exceptional quality and communication throughout",
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      title: "Product Lead, RetailHub Kenya",
      image: "/pexels-mikhail-nilov-6893951.jpg",
      quote: "Best development partner we have worked with",
    },
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="mb-12 text-center">
        <h2 className="mb-4 text-4xl font-bold">Our Customers</h2>
        <p className="text-lg text-muted-foreground">Trusted by industry leaders</p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {customers.map((customer, index) => (
          <div key={index} className="group relative overflow-hidden rounded-lg">
            <img
              src={customer.image || "/placeholder.svg"}
              alt={customer.name}
              className="h-[400px] w-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background via-background/60 to-transparent">
              <div className="absolute bottom-0 left-0 right-0 p-6">
                <p className="mb-3 text-sm italic text-muted-foreground">&ldquo;{customer.quote}&rdquo;</p>
                <h3 className="text-lg font-semibold">{customer.name}</h3>
                <p className="text-sm text-muted-foreground">{customer.title}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  )
}
