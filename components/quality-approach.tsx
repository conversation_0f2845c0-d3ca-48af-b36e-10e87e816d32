export function QualityApproach() {
  return (
    <section className="container mx-auto px-6 py-20">
      <div className="mb-12 text-center">
        <h2 className="mb-4 text-4xl font-bold text-balance">Our Approach to Quality Custom Solutions</h2>
        <p className="text-lg text-muted-foreground">Proven methodologies that deliver results</p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="rounded-lg border border-border bg-card p-8 text-center">
          <div className="mb-4 text-5xl font-bold text-accent">01</div>
          <h3 className="mb-3 text-xl font-semibold">Discovery & Planning</h3>
          <p className="text-muted-foreground">Deep dive into your requirements, goals, and technical landscape</p>
        </div>
        <div className="rounded-lg border border-border bg-card p-8 text-center">
          <div className="mb-4 text-5xl font-bold text-accent">02</div>
          <h3 className="mb-3 text-xl font-semibold">Agile Development</h3>
          <p className="text-muted-foreground">Iterative sprints with continuous feedback and adaptation</p>
        </div>
        <div className="rounded-lg border border-border bg-card p-8 text-center">
          <div className="mb-4 text-5xl font-bold text-accent">03</div>
          <h3 className="mb-3 text-xl font-semibold">Launch & Support</h3>
          <p className="text-muted-foreground">Smooth deployment with ongoing maintenance and optimization</p>
        </div>
      </div>
    </section>
  )
}
