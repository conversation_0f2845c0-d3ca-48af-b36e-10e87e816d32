"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function NewHeroSection() {
  return (
    <section className="container mx-auto px-6 pt-32 pb-20">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <div className="mb-6 inline-block">
            <span className="text-sm font-medium text-accent">Your Technology Partner</span>
          </div>
          <h1 className="mb-6 text-5xl font-bold leading-tight text-balance lg:text-6xl">
            We Build Software & Drive Customers to Your Business
          </h1>
          <p className="mb-8 text-xl text-muted-foreground leading-relaxed text-pretty max-w-4xl mx-auto">
            From custom software development to marketing automation that actually gets you customers.
            We're the technical partner that helps Kenyan businesses scale.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              asChild
              size="lg"
              className="rounded-full bg-accent text-accent-foreground hover:bg-accent/90 px-8"
            >
              <Link href="#services">Explore Services</Link>
            </Button>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="rounded-full border-2 border-accent bg-transparent hover:bg-accent/10"
            >
              <Link href="/#contact">Book Consultation</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}
