interface Step {
  number: string
  title: string
  description: string
}

interface ServiceHowItWorksProps {
  steps: Step[]
}

export function ServiceHowItWorks({ steps }: ServiceHowItWorksProps) {
  return (
    <section id="how-it-works" className="container mx-auto px-6 py-20 bg-muted/30">
      <div className="max-w-4xl mx-auto">
        <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">How It Works</h2>
        <div className="space-y-8">
          {steps.map((step, index) => (
            <div key={index} className="flex gap-6 items-start">
              <div className="flex-shrink-0 w-12 h-12 rounded-full bg-accent/20 border-2 border-accent flex items-center justify-center">
                <span className="text-xl font-bold text-accent">{step.number}</span>
              </div>
              <div className="flex-1">
                <h3 className="mb-2 text-xl font-semibold">{step.title}</h3>
                <p className="text-muted-foreground leading-relaxed">{step.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
