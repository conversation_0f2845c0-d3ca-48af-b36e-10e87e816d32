import { Button } from "@/components/ui/button"
import { ArrowRightIcon } from "@/components/icons"

export function ProjectsShowcase() {
  const projects = [
    {
      title: "Healthcare Platform",
      description: "HIPAA-compliant telemedicine solution serving 50,000+ patients",
      image: "/modern-healthcare-dashboard.jpg",
    },
    {
      title: "FinTech Application",
      description: "Real-time payment processing system handling $10M+ daily transactions",
      image: "/financial-dashboard-charts.jpg",
    },
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="mb-12 flex items-center justify-between">
        <div>
          <h2 className="mb-4 text-4xl font-bold">Claims Backed by Success Stories</h2>
          <p className="text-lg text-muted-foreground">Real results from real partnerships</p>
        </div>
        <Button variant="outline" className="rounded-full border-accent text-accent hover:bg-accent/10 bg-transparent">
          View All Work
          <ArrowRightIcon className="ml-2 h-4 w-4" />
        </Button>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        {projects.map((project, index) => (
          <div key={index} className="group relative overflow-hidden rounded-lg">
            <img
              src={project.image || "/placeholder.svg"}
              alt={project.title}
              className="h-[400px] w-full object-cover transition-transform group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background/95 via-background/50 to-transparent">
              <div className="absolute bottom-0 left-0 right-0 p-8">
                <h3 className="mb-2 text-2xl font-bold">{project.title}</h3>
                <p className="mb-4 text-muted-foreground">{project.description}</p>
                <Button variant="link" className="p-0 text-accent hover:text-accent/80">
                  View Case Study
                  <ArrowRightIcon className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  )
}
