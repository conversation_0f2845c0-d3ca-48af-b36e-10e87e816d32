import Link from "next/link"
import { Code2Icon } from "@/components/icons"

export function Footer() {
  return (
    <footer className="border-t border-border bg-card/50">
      <div className="container mx-auto px-6 py-12">
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-5">
          <div className="lg:col-span-2">
            <Link href="/" className="mb-4 flex items-center gap-2">
              <Code2Icon className="h-8 w-8 text-accent" />
              <span className="text-xl font-bold">Nexveria</span>
            </Link>
            <p className="mb-4 text-sm text-muted-foreground">
              Custom Software Development Company Focused on Your Success
            </p>
            <div className="text-sm text-muted-foreground">
              <p className="font-semibold">Headquarters</p>
              <p>Kisumu, Kenya</p>
            </div>
          </div>

          <div>
            <h4 className="mb-4 font-semibold">Engineering</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link href="/services/custom-software" className="hover:text-accent">
                  Custom Software
                </Link>
              </li>
              <li>
                <Link href="/services/cloud-engineering" className="hover:text-accent">
                  Cloud Engineering
                </Link>
              </li>
              <li>
                <Link href="/services/application-modernization" className="hover:text-accent">
                  App Modernization
                </Link>
              </li>
              <li>
                <Link href="/services/devops" className="hover:text-accent">
                  DevOps & Automation
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="mb-4 font-semibold">Data & AI</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link href="/services/ai-development" className="hover:text-accent">
                  AI Development
                </Link>
              </li>
              <li>
                <Link href="/services/ai-chatbot" className="hover:text-accent">
                  AI Chatbot
                </Link>
              </li>
              <li>
                <Link href="/services/data-engineering" className="hover:text-accent">
                  Data Engineering
                </Link>
              </li>
              <li>
                <Link href="/services/business-intelligence" className="hover:text-accent">
                  Business Intelligence
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="mb-4 font-semibold">Marketing & Growth</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>
                <Link href="/services/whatsapp-automation" className="hover:text-accent">
                  WhatsApp Automation
                </Link>
              </li>
              <li>
                <Link href="/services/social-media-management" className="hover:text-accent">
                  Social Media
                </Link>
              </li>
              <li>
                <Link href="/services/google-ads" className="hover:text-accent">
                  Google Ads
                </Link>
              </li>
              <li>
                <Link href="/services/facebook-ads" className="hover:text-accent">
                  Facebook Ads
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 flex flex-col items-center justify-between gap-4 border-t border-border pt-8 text-sm text-muted-foreground md:flex-row">
          <p>All Rights Reserved. Nexveria.</p>
          <div className="flex gap-6">
            <Link href="#" className="hover:text-accent">
              Privacy Policy
            </Link>
            <Link href="#" className="hover:text-accent">
              Terms of Use
            </Link>
            <Link href="#" className="hover:text-accent">
              Sitemap
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
