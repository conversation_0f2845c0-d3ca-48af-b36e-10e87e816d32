"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Check } from "lucide-react"
import Link from "next/link"

interface PricingTier {
  name: string
  price: string
  period?: string
  description: string
  features: string[]
  highlighted?: boolean
  ctaText?: string
}

interface ServicePricingProps {
  title: string
  subtitle?: string
  tiers: PricingTier[]
}

export function ServicePricing({ title, subtitle, tiers }: ServicePricingProps) {
  return (
    <section className="container mx-auto px-6 py-20">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="mb-4 text-3xl font-bold lg:text-4xl">{title}</h2>
          {subtitle && <p className="text-lg text-muted-foreground">{subtitle}</p>}
        </div>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {tiers.map((tier, index) => (
            <div
              key={index}
              className={`rounded-lg border bg-card p-8 ${
                tier.highlighted
                  ? "border-accent shadow-[0_0_30px_rgba(112,224,200,0.2)] scale-105"
                  : "border-border"
              }`}
            >
              {tier.highlighted && (
                <div className="mb-4 inline-block rounded-full bg-accent/20 px-3 py-1 text-sm font-medium text-accent">
                  Most Popular
                </div>
              )}
              <h3 className="mb-2 text-2xl font-bold">{tier.name}</h3>
              <div className="mb-4">
                <span className="text-4xl font-bold">{tier.price}</span>
                {tier.period && <span className="text-muted-foreground">/{tier.period}</span>}
              </div>
              <p className="mb-6 text-muted-foreground">{tier.description}</p>
              <ul className="mb-8 space-y-3">
                {tier.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start gap-3">
                    <Check className="h-5 w-5 text-accent flex-shrink-0 mt-0.5" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
              <Button
                asChild
                className={`w-full rounded-full ${
                  tier.highlighted
                    ? "bg-accent text-accent-foreground hover:bg-accent/90"
                    : "bg-transparent border-2 border-accent hover:bg-accent/10"
                }`}
              >
                <Link href="#contact">{tier.ctaText || "Get Started"}</Link>
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
