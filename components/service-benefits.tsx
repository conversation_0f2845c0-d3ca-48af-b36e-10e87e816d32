interface Benefit {
  title: string
  description: string
  metric?: string
}

interface ServiceBenefitsProps {
  title: string
  benefits: Benefit[]
}

export function ServiceBenefits({ title, benefits }: ServiceBenefitsProps) {
  return (
    <section className="container mx-auto px-6 py-20">
      <div className="max-w-6xl mx-auto">
        <h2 className="mb-12 text-3xl font-bold text-center lg:text-4xl">{title}</h2>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="rounded-lg border border-border bg-card p-6 hover:border-accent/50 transition-colors"
            >
              {benefit.metric && (
                <div className="mb-3 text-3xl font-bold text-accent">{benefit.metric}</div>
              )}
              <h3 className="mb-3 text-xl font-semibold">{benefit.title}</h3>
              <p className="text-muted-foreground leading-relaxed">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
