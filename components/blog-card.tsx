import Link from "next/link"
import { BlogPost } from "@/lib/blog-posts"
import { Calendar, Clock, User } from "lucide-react"

interface BlogCardProps {
  post: BlogPost
}

export function BlogCard({ post }: BlogCardProps) {
  return (
    <article className="group rounded-lg border border-border bg-card overflow-hidden hover:border-accent/50 transition-colors">
      <div className="p-6">
        <div className="mb-3 flex items-center gap-4 text-sm text-muted-foreground">
          <span className="inline-flex items-center gap-1.5">
            <Calendar className="h-4 w-4" />
            {new Date(post.date).toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
              year: "numeric",
            })}
          </span>
          <span className="inline-flex items-center gap-1.5">
            <Clock className="h-4 w-4" />
            {post.readTime}
          </span>
        </div>

        <div className="mb-2">
          <span className="inline-block rounded-full bg-accent/20 px-3 py-1 text-xs font-medium text-accent">
            {post.category}
          </span>
        </div>

        <Link href={`/blog/${post.slug}`} className="block">
          <h3 className="mb-3 text-xl font-bold leading-tight group-hover:text-accent transition-colors">
            {post.title}
          </h3>
        </Link>

        <p className="mb-4 text-muted-foreground leading-relaxed line-clamp-3">{post.description}</p>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <User className="h-4 w-4" />
            <span>{post.author}</span>
          </div>
          <Link
            href={`/blog/${post.slug}`}
            className="text-sm font-medium text-accent hover:underline"
          >
            Read More →
          </Link>
        </div>
      </div>
    </article>
  )
}
