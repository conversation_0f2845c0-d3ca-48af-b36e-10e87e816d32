import { AlertCircleIcon } from "@/components/icons"

export function ProblemCards() {
  const problems = [
    {
      title: "Struggling to Build the Right Team?",
      description: "Finding skilled developers who understand your domain is challenging and time-consuming.",
    },
    {
      title: "Behind Schedule on Your MVP?",
      description: "Delays compound quickly, pushing back your market entry and competitive advantage.",
    },
    {
      title: "Confused About Tech Requirements?",
      description: "Choosing the right technology stack and architecture can make or break your project.",
    },
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="grid gap-6 md:grid-cols-3">
        {problems.map((problem, index) => (
          <div key={index} className="rounded-lg border-2 border-destructive/30 bg-card p-8">
            <AlertCircleIcon className="mb-4 h-10 w-10 text-destructive" />
            <h3 className="mb-3 text-xl font-semibold">{problem.title}</h3>
            <p className="text-muted-foreground leading-relaxed">{problem.description}</p>
          </div>
        ))}
      </div>
    </section>
  )
}
