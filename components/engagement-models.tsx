import { Button } from "@/components/ui/button"
import { BriefcaseIcon, UsersIcon } from "@/components/icons"

export function EngagementModels() {
  const models = [
    {
      icon: BriefcaseIcon,
      title: "Project-Based Delivery",
      description: "Fixed scope, timeline, and budget for well-defined projects with clear deliverables.",
      buttonText: "Start a Project",
    },
    {
      icon: UsersIcon,
      title: "Dedicated Teams",
      description: "Flexible, scalable teams that integrate with your organization for ongoing development.",
      buttonText: "Build Your Team",
    },
  ]

  return (
    <section className="container mx-auto px-6 py-20">
      <div className="mb-12 text-center">
        <h2 className="mb-4 text-4xl font-bold">{"Let's"} Engage</h2>
        <p className="text-lg text-muted-foreground">Choose the model that fits your needs</p>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        {models.map((model, index) => {
          const Icon = model.icon
          return (
            <div key={index} className="rounded-lg border-2 border-accent/30 bg-card p-8">
              <Icon className="mb-6 h-12 w-12 text-accent" />
              <h3 className="mb-4 text-2xl font-semibold">{model.title}</h3>
              <p className="mb-6 text-muted-foreground leading-relaxed">{model.description}</p>
              <Button className="border-2 border-accent bg-transparent text-accent hover:bg-accent/10">
                {model.buttonText}
              </Button>
            </div>
          )
        })}
      </div>
    </section>
  )
}
