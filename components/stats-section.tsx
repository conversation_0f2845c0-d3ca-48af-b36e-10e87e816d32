interface Stat {
  value: string
  label: string
}

interface StatsSectionProps {
  stats?: Stat[]
  title?: string
  subtitle?: string
  className?: string
}

export function StatsSection({
  stats = [
    { value: "30%", label: "Cost Reduction" },
    { value: "8+", label: "Years Experience" },
    { value: "30+", label: "Projects Delivered" },
    { value: "50+", label: "Happy Clients" },
  ],
  title,
  subtitle,
  className = ""
}: StatsSectionProps) {
  return (
    <section className={`border-y border-border bg-card/30 py-16 ${className}`}>
      <div className="container mb-12 mx-auto px-6">
        {title && (
          <h2 className="mb-4 text-center text-3xl font-bold lg:text-4xl">{title}</h2>
        )}
        {subtitle && (
          <p className="text-lg text-center text-muted-foreground">{subtitle}</p>
        )}
      </div>
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="mb-2 text-5xl font-bold text-accent">{stat.value}</div>
              <div className="text-sm text-muted-foreground">{stat.label}</div>
            </div>
          ))}
        </div>
    </section>
  )
}
